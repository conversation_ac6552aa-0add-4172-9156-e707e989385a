import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  Alert,
  ScrollView,
} from 'react-native';
import { doc, updateDoc, addDoc, collection, serverTimestamp, Timestamp } from 'firebase/firestore';
import { db } from '../config/firebase';
import QuestionStage from './QuestionStage';
import MysteryStage from './MysteryStage';

const StageManager = ({ letter, letterAttempt, navigation }) => {
  const [loading, setLoading] = useState(false);
  const [feedback, setFeedback] = useState('');

  const handleAnswer = async (answer) => {
    if (loading) return;

    setLoading(true);
    setFeedback('');

    try {
      const currentStage = letter.stages[letter.currentStage];
      let isCorrect = false;

      if (currentStage.type === 'question') {
        isCorrect = answer.toLowerCase().trim() === currentStage.answer.toLowerCase().trim();
      } else if (currentStage.type === 'mystery') {
        isCorrect = answer.toLowerCase().trim() === currentStage.correctPerson.toLowerCase().trim();
      }

      if (isCorrect) {
        setFeedback('Doğru! Bir sonraki aşamaya geçiliyor...');
        
        const newCurrentStage = letter.currentStage + 1;
        const isCompleted = newCurrentStage >= letter.stages.length;

        await updateDoc(doc(db, 'letters', letter.id), {
          currentStage: newCurrentStage,
          isCompleted,
          updatedAt: serverTimestamp(),
        });

        if (letterAttempt) {
          await updateDoc(doc(db, 'letterAttempts', letterAttempt.id), {
            lockedUntil: null,
            updatedAt: serverTimestamp(),
          });
        }

        setTimeout(() => {
          if (isCompleted) {
            navigation.replace('FinalLetter', { letter: { ...letter, isCompleted: true } });
          } else {
            setFeedback('');
          }
        }, 2000);

      } else {
        setFeedback('Yanlış cevap! Mektup 24 saatliğine kilitlendi.');
        
        const lockUntil = new Date();
        lockUntil.setHours(lockUntil.getHours() + 24);

        if (letterAttempt) {
          await updateDoc(doc(db, 'letterAttempts', letterAttempt.id), {
            lockedUntil: Timestamp.fromDate(lockUntil),
            wrongAnswers: (letterAttempt.wrongAnswers || 0) + 1,
            updatedAt: serverTimestamp(),
          });
        } else {
          await addDoc(collection(db, 'letterAttempts'), {
            userId: 'anonymous-user',
            letterId: letter.id,
            lockedUntil: Timestamp.fromDate(lockUntil),
            wrongAnswers: 1,
            createdAt: serverTimestamp(),
            updatedAt: serverTimestamp(),
          });
        }

        setTimeout(() => {
          navigation.goBack();
        }, 2000);
      }

    } catch (error) {
      console.error('Error handling answer:', error);
      Alert.alert('Hata', 'Cevap işlenirken bir hata oluştu.');
    } finally {
      setLoading(false);
    }
  };

  if (!letter.stages || letter.stages.length === 0) {
    return <View style={styles.errorContainer}><Text style={styles.errorText}>Bu mektupta aşama bulunamadı.</Text></View>;
  }

  if (letter.currentStage >= letter.stages.length) {
    return <View style={styles.errorContainer}><Text style={styles.errorText}>Tüm aşamalar tamamlandı.</Text></View>;
  }

  const currentStage = letter.stages[letter.currentStage];

  return (
    <ScrollView style={styles.container} contentContainerStyle={styles.contentContainer}>
        <View style={styles.stageHeader}>
            <Text style={styles.stageTitle}>
                {currentStage.type === 'question' ? 'Şifreyi Çöz' : 'Suçluyu Bul'}
            </Text>
            <Text style={styles.stageSubtitle}>Aşama {letter.currentStage + 1}</Text>
        </View>

        {feedback ? (
            <View style={[styles.feedbackContainer, feedback.includes('Doğru') ? styles.successFeedback : styles.errorFeedback]}>
                <Text style={styles.feedbackText}>{feedback}</Text>
            </View>
        ) : null}

        <View style={styles.stageContent}>
            {currentStage.type === 'question' ? (
                <QuestionStage stage={currentStage} onAnswer={handleAnswer} loading={loading} />
            ) : (
                <MysteryStage stage={currentStage} onAnswer={handleAnswer} loading={loading} />
            )}
        </View>
    </ScrollView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f8fafc',
  },
  contentContainer: {
    padding: 20,
    paddingBottom: 100,
  },
  errorContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 40,
  },
  errorText: {
    fontSize: 16,
    color: '#ef4444',
    textAlign: 'center',
  },
  stageHeader: {
    alignItems: 'center',
    marginBottom: 24,
  },
  stageTitle: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#1e293b',
    textAlign: 'center',
  },
  stageSubtitle: {
    fontSize: 16,
    color: '#64748b',
    marginTop: 4,
  },
  feedbackContainer: {
    marginVertical: 16,
    padding: 16,
    borderRadius: 12,
    alignItems: 'center',
  },
  successFeedback: {
    backgroundColor: '#dcfce7',
    borderColor: '#4ade80',
    borderWidth: 1,
  },
  errorFeedback: {
    backgroundColor: '#fee2e2',
    borderColor: '#f87171',
    borderWidth: 1,
  },
  feedbackText: {
    fontSize: 16,
    fontWeight: '600',
    textAlign: 'center',
    color: '#1e293b',
  },
  stageContent: {
    flex: 1,
  },
});

export default StageManager;