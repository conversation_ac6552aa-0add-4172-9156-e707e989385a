import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  Alert,
} from 'react-native';
import { doc, updateDoc, addDoc, collection, serverTimestamp, Timestamp } from 'firebase/firestore';
import { db } from '../config/firebase';
import QuestionStage from './QuestionStage';
import MysteryStage from './MysteryStage';

const StageManager = ({ letter, letterAttempt, navigation }) => {
  const [loading, setLoading] = useState(false);
  const [feedback, setFeedback] = useState('');

  const handleAnswer = async (answer) => {
    if (loading) return;

    setLoading(true);
    setFeedback('');

    try {
      const currentStage = letter.stages[letter.currentStage];
      let isCorrect = false;

      // Cevabı kontrol et
      if (currentStage.type === 'question') {
        isCorrect = answer.toLowerCase().trim() === currentStage.answer.toLowerCase().trim();
      } else if (currentStage.type === 'mystery') {
        isCorrect = answer.toLowerCase().trim() === currentStage.correctPerson.toLowerCase().trim();
      }

      if (isCorrect) {
        setFeedback('Doğru! Bir sonraki aşamaya geçiliyor...');
        
        // Doğru cevap - aşamayı ilerlet
        const newCurrentStage = letter.currentStage + 1;
        const isCompleted = newCurrentStage >= letter.stages.length;

        await updateDoc(doc(db, 'letters', letter.id), {
          currentStage: newCurrentStage,
          isCompleted,
          updatedAt: serverTimestamp(),
        });

        // Letter attempt'i temizle (kilitli durumu kaldır)
        if (letterAttempt) {
          await updateDoc(doc(db, 'letterAttempts', letterAttempt.id), {
            lockedUntil: null,
            updatedAt: serverTimestamp(),
          });
        }

        setTimeout(() => {
          if (isCompleted) {
            navigation.replace('FinalLetter', { letter: { ...letter, isCompleted: true } });
          } else {
            setFeedback('');
          }
        }, 2000);

      } else {
        setFeedback('Yanlış cevap! Mektup 24 saatliğine kilitlendi.');
        
        // Yanlış cevap - mektubu kilitle
        const lockUntil = new Date();
        lockUntil.setHours(lockUntil.getHours() + 24);

        if (letterAttempt) {
          // Mevcut attempt'i güncelle
          await updateDoc(doc(db, 'letterAttempts', letterAttempt.id), {
            lockedUntil: Timestamp.fromDate(lockUntil),
            wrongAnswers: (letterAttempt.wrongAnswers || 0) + 1,
            updatedAt: serverTimestamp(),
          });
        } else {
          // Yeni attempt oluştur
          await addDoc(collection(db, 'letterAttempts'), {
            userId: 'anonymous-user',
            letterId: letter.id,
            lockedUntil: Timestamp.fromDate(lockUntil),
            wrongAnswers: 1,
            createdAt: serverTimestamp(),
            updatedAt: serverTimestamp(),
          });
        }

        setTimeout(() => {
          navigation.goBack();
        }, 2000);
      }

    } catch (error) {
      console.error('Error handling answer:', error);
      Alert.alert('Hata', 'Cevap işlenirken bir hata oluştu.');
    } finally {
      setLoading(false);
    }
  };

  if (!letter.stages || letter.stages.length === 0) {
    return (
      <View style={styles.errorContainer}>
        <Text style={styles.errorText}>Bu mektupte aşama bulunamadı</Text>
      </View>
    );
  }

  if (letter.currentStage >= letter.stages.length) {
    return (
      <View style={styles.errorContainer}>
        <Text style={styles.errorText}>Tüm aşamalar tamamlandı</Text>
      </View>
    );
  }

  const currentStage = letter.stages[letter.currentStage];

  return (
    <View style={styles.container}>
      <View style={styles.stageHeader}>
        <Text style={styles.stageTitle}>
          Aşama {letter.currentStage + 1}: {
            currentStage.type === 'question' ? 'Şifreyi Çöz' : 'Suçluyu Bul'
          }
        </Text>
      </View>

      {feedback ? (
        <View style={[
          styles.feedbackContainer,
          feedback.includes('Doğru') ? styles.successFeedback : styles.errorFeedback
        ]}>
          <Text style={[
            styles.feedbackText,
            feedback.includes('Doğru') ? styles.successText : styles.errorText
          ]}>
            {feedback}
          </Text>
        </View>
      ) : null}

      <View style={styles.stageContent}>
        {currentStage.type === 'question' ? (
          <QuestionStage
            stage={currentStage}
            onAnswer={handleAnswer}
            loading={loading}
          />
        ) : (
          <MysteryStage
            stage={currentStage}
            onAnswer={handleAnswer}
            loading={loading}
          />
        )}
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  errorContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 40,
  },
  errorText: {
    fontSize: 16,
    color: '#e53e3e',
    textAlign: 'center',
  },
  stageHeader: {
    backgroundColor: 'white',
    padding: 20,
    borderBottomWidth: 1,
    borderBottomColor: '#e2e8f0',
  },
  stageTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#2d3748',
    textAlign: 'center',
  },
  feedbackContainer: {
    margin: 20,
    padding: 16,
    borderRadius: 12,
    alignItems: 'center',
  },
  successFeedback: {
    backgroundColor: '#c6f6d5',
  },
  errorFeedback: {
    backgroundColor: '#fed7d7',
  },
  feedbackText: {
    fontSize: 16,
    fontWeight: '600',
    textAlign: 'center',
  },
  successText: {
    color: '#22543d',
  },
  stageContent: {
    flex: 1,
  },
});

export default StageManager;
