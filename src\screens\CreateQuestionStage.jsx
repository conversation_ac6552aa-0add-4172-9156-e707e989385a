import React, { useState } from 'react';
import {
  View,
  Text,
  TextInput,
  TouchableOpacity,
  StyleSheet,
  ScrollView,
  Alert,
  KeyboardAvoidingView,
  Platform,
} from 'react-native';

const CreateQuestionStage = ({ navigation, route }) => {
  const { stages: initialStages = [], type } = route.params;
  const [stages, setStages] = useState(initialStages);
  const [currentStage, setCurrentStage] = useState({
    question: '',
    answer: '',
    hint: '',
  });

  const handleAddStage = () => {
    if (!currentStage.question.trim() || !currentStage.answer.trim()) {
      Alert.alert('<PERSON>ksik Bilgi', 'Lütfen soru ve cevap alanlarını doldurun.');
      return;
    }

    const newStages = [...stages, { ...currentStage, type: 'question' }];
    setStages(newStages);
    
    // Reset form for the next stage
    setCurrentStage({
      question: '',
      answer: '',
      hint: '',
    });

    // Update the stages in the navigation params as well
    navigation.setParams({ stages: newStages });
  };

  const handleRemoveStage = (index) => {
    Alert.alert(
      'Aşamayı Sil',
      'Bu aşamayı silmek istediğinizden emin misiniz?',
      [
        { text: 'İptal', style: 'cancel' },
        {
          text: 'Sil',
          style: 'destructive',
          onPress: () => {
            const newStages = stages.filter((_, i) => i !== index);
            setStages(newStages);
            navigation.setParams({ stages: newStages });
          },
        },
      ]
    );
  };

  const handleFinish = () => {
    if (stages.length === 0) {
      Alert.alert('Eksik Bilgi', 'Devam etmek için en az bir aşama eklemelisiniz.');
      return;
    }

    navigation.navigate('FinalEditor', { stages, type });
  };

  const renderStagePreview = (stage, index) => (
    <View key={index} style={styles.stagePreview}>
      <View style={styles.stageHeader}>
        <Text style={styles.stageTitle}>Aşama {index + 1}</Text>
        <TouchableOpacity
          style={styles.deleteButton}
          onPress={() => handleRemoveStage(index)}
        >
          <Text style={styles.deleteButtonText}>🗑️</Text>
        </TouchableOpacity>
      </View>
      <Text style={styles.stageQuestion} numberOfLines={2}>{stage.question}</Text>
      <Text style={styles.stageAnswer}>Cevap: {stage.answer}</Text>
      {stage.hint ? <Text style={styles.stageHint}>İpucu: {stage.hint}</Text> : null}
    </View>
  );

  return (
    <KeyboardAvoidingView
      style={styles.container}
      behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
    >
      <ScrollView style={styles.scrollContainer} contentContainerStyle={styles.contentContainer} showsVerticalScrollIndicator={false}>
        <View style={styles.header}>
            <Text style={styles.headerIcon}>❓</Text>
            <Text style={styles.title}>Soru Aşaması Ekle</Text>
            <Text style={styles.subtitle}>Soru ve cevap tabanlı aşamalar oluşturun</Text>
        </View>

        <View style={styles.card}>
            <Text style={styles.cardTitle}>Aşama {stages.length + 1}</Text>
            <View style={styles.inputGroup}>
                <Text style={styles.label}>Soru *</Text>
                <TextInput
                style={styles.textArea}
                placeholder="Sorunuzu buraya yazın..."
                value={currentStage.question}
                onChangeText={(text) => setCurrentStage({ ...currentStage, question: text })}
                multiline
                />
            </View>

            <View style={styles.inputGroup}>
                <Text style={styles.label}>Doğru Cevap *</Text>
                <TextInput
                style={styles.input}
                placeholder="Doğru cevabı buraya girin"
                value={currentStage.answer}
                onChangeText={(text) => setCurrentStage({ ...currentStage, answer: text })}
                />
            </View>

            <View style={styles.inputGroup}>
                <Text style={styles.label}>İpucu (İsteğe bağlı)</Text>
                <TextInput
                style={styles.input}
                placeholder="İpucu metni..."
                value={currentStage.hint}
                onChangeText={(text) => setCurrentStage({ ...currentStage, hint: text })}
                />
            </View>

            <TouchableOpacity style={styles.addButton} onPress={handleAddStage}>
                <Text style={styles.addButtonText}>+ Yeni Aşama Ekle</Text>
            </TouchableOpacity>
        </View>

        {stages.length > 0 && (
          <View style={styles.previewContainer}>
            <Text style={styles.previewTitle}>Eklenen Aşamalar ({stages.length})</Text>
            {stages.map(renderStagePreview)}
          </View>
        )}

        {stages.length > 0 && (
            <TouchableOpacity style={styles.finishButton} onPress={handleFinish}>
              <Text style={styles.finishButtonText}>Kaydet ve Bitir</Text>
            </TouchableOpacity>
        )}
      </ScrollView>
    </KeyboardAvoidingView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f0f4f8',
  },
  scrollContainer: {
    flex: 1,
  },
  contentContainer: {
    paddingHorizontal: 20,
    paddingBottom: 120,
  },
  header: {
    paddingTop: 40,
    paddingBottom: 30,
    paddingHorizontal: 20,
    alignItems: 'center',
    backgroundColor: '#181b29ff', // Blue color
    borderBottomLeftRadius: 30,
    borderBottomRightRadius: 30,
    marginBottom: 24,
    marginHorizontal: -20, // Full-width header
  },
  headerIcon: {
    fontSize: 48,
    marginBottom: 8,
  },
  title: {
    fontSize: 28,
    fontWeight: 'bold',
    color: 'white',
    textAlign: 'center',
  },
  subtitle: {
    fontSize: 16,
    color: 'rgba(255, 255, 255, 0.9)',
    textAlign: 'center',
    marginTop: 8,
  },
  card: {
    backgroundColor: 'white',
    borderRadius: 16,
    padding: 20,
    marginBottom: 24,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 4,
  },
  cardTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#1e293b',
    marginBottom: 16,
    textAlign: 'center',
  },
  inputGroup: {
    marginBottom: 16,
  },
  label: {
    fontSize: 16,
    fontWeight: '600',
    color: '#334155',
    marginBottom: 8,
  },
  input: {
    borderWidth: 1,
    borderColor: '#e2e8f0',
    borderRadius: 12,
    padding: 14,
    fontSize: 16,
    backgroundColor: '#f8fafc',
  },
  textArea: {
    borderWidth: 1,
    borderColor: '#e2e8f0',
    borderRadius: 12,
    padding: 14,
    fontSize: 16,
    backgroundColor: '#f8fafc',
    minHeight: 100,
    textAlignVertical: 'top',
  },
  addButton: {
    backgroundColor: '#3b82f6',
    borderRadius: 12,
    padding: 16,
    alignItems: 'center',
    marginTop: 8,
  },
  addButtonText: {
    color: 'white',
    fontSize: 16,
    fontWeight: 'bold',
  },
  previewContainer: {
    marginTop: 16,
  },
  previewTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#1e293b',
    marginBottom: 12,
    paddingLeft: 5,
  },
  stagePreview: {
    backgroundColor: 'white',
    borderRadius: 12,
    padding: 16,
    marginBottom: 12,
    borderLeftWidth: 4,
    borderLeftColor: '#3b82f6',
  },
  stageHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 8,
  },
  stageTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#1e40af',
  },
  deleteButton: {
    padding: 4,
  },
  deleteButtonText: {
    fontSize: 18,
  },
  stageQuestion: {
    fontSize: 15,
    color: '#334155',
    marginBottom: 6,
  },
  stageAnswer: {
    fontSize: 14,
    color: '#16a34a',
    fontWeight: '600',
    marginBottom: 4,
  },
  stageHint: {
    fontSize: 14,
    color: '#64748b',
    fontStyle: 'italic',
    marginTop: 4,
  },
  finishButton: {
    backgroundColor: '#22c55e',
    borderRadius: 12,
    padding: 18,
    alignItems: 'center',
    marginTop: 16,
  },
  finishButtonText: {
    color: 'white',
    fontSize: 18,
    fontWeight: 'bold',
  },
});

export default CreateQuestionStage;