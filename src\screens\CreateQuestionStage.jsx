import React, { useState } from 'react';
import {
  View,
  Text,
  TextInput,
  TouchableOpacity,
  StyleSheet,
  ScrollView,
  Alert,
  KeyboardAvoidingView,
  Platform,
} from 'react-native';

const CreateQuestionStage = ({ navigation, route }) => {
  const { stages = [], type } = route.params;
  const [currentStage, setCurrentStage] = useState({
    question: '',
    answer: '',
    hint: '',
  });

  const handleAddStage = () => {
    if (!currentStage.question.trim() || !currentStage.answer.trim()) {
      Alert.alert('Eksik Bilgi', 'Lütfen soru ve cevap alanlarını doldurun.');
      return;
    }

    const newStages = [...stages, { ...currentStage, type: 'question' }];
    
    // Yeni boş aşama formu
    setCurrentStage({
      question: '',
      answer: '',
      hint: '',
    });

    // Parametreleri güncelle
    navigation.setParams({ stages: newStages });
  };

  const handleRemoveStage = (index) => {
    Alert.alert(
      'Aşamayı Sil',
      '<PERSON><PERSON> aşamayı silmek istediğinizden emin misiniz?',
      [
        { text: 'İptal', style: 'cancel' },
        {
          text: 'Sil',
          style: 'destructive',
          onPress: () => {
            const newStages = stages.filter((_, i) => i !== index);
            navigation.setParams({ stages: newStages });
          },
        },
      ]
    );
  };

  const handleFinish = () => {
    if (stages.length === 0) {
      Alert.alert('Eksik Bilgi', 'En az bir aşama eklemelisiniz.');
      return;
    }

    navigation.navigate('FinalEditor', { stages, type });
  };

  const renderStagePreview = (stage, index) => (
    <View key={index} style={styles.stagePreview}>
      <View style={styles.stageHeader}>
        <Text style={styles.stageTitle}>Aşama {index + 1}</Text>
        <TouchableOpacity
          style={styles.deleteButton}
          onPress={() => handleRemoveStage(index)}
        >
          <Text style={styles.deleteButtonText}>🗑️</Text>
        </TouchableOpacity>
      </View>
      <Text style={styles.stageQuestion} numberOfLines={2}>
        {stage.question}
      </Text>
      <Text style={styles.stageAnswer}>Cevap: {stage.answer}</Text>
      {stage.hint ? (
        <Text style={styles.stageHint}>İpucu: {stage.hint}</Text>
      ) : null}
    </View>
  );

  return (
    <KeyboardAvoidingView
      style={styles.container}
      behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
    >
      <ScrollView style={styles.scrollContainer} showsVerticalScrollIndicator={false}>
        <View style={styles.header}>
          <View style={styles.headerContent}>
            <Text style={styles.headerIcon}>❓</Text>
            <Text style={styles.title}>Soru Aşaması Ekle</Text>
            <Text style={styles.stageNumber}>Aşama {stages.length + 1}</Text>
          </View>
          <Text style={styles.subtitle}>
            Soru ve cevap tabanlı aşama oluşturun
          </Text>
        </View>

        <View style={styles.formContainer}>
          <View style={styles.inputGroup}>
            <Text style={styles.label}>Soru *</Text>
            <TextInput
              style={[styles.textArea, styles.questionInput]}
              placeholder="Sorunuzu buraya yazın..."
              value={currentStage.question}
              onChangeText={(text) => setCurrentStage({ ...currentStage, question: text })}
              multiline
              numberOfLines={4}
              textAlignVertical="top"
            />
          </View>

          <View style={styles.inputGroup}>
            <Text style={styles.label}>Doğru Cevap *</Text>
            <TextInput
              style={styles.input}
              placeholder="Doğru cevabı buraya girin"
              value={currentStage.answer}
              onChangeText={(text) => setCurrentStage({ ...currentStage, answer: text })}
            />
          </View>

          <View style={styles.inputGroup}>
            <Text style={styles.label}>İpucu (İsteğe bağlı)</Text>
            <TextInput
              style={styles.input}
              placeholder="İpucu (isteğe bağlı)"
              value={currentStage.hint}
              onChangeText={(text) => setCurrentStage({ ...currentStage, hint: text })}
            />
          </View>

          <TouchableOpacity style={styles.addButton} onPress={handleAddStage}>
            <Text style={styles.addButtonText}>Aşama Ekle</Text>
          </TouchableOpacity>
        </View>

        {stages.length > 0 && (
          <View style={styles.previewContainer}>
            <Text style={styles.previewTitle}>Eklenen Aşamalar ({stages.length})</Text>
            {stages.map(renderStagePreview)}
          </View>
        )}

        <View style={styles.bottomButtons}>
          {stages.length > 0 && (
            <TouchableOpacity style={styles.finishButton} onPress={handleFinish}>
              <Text style={styles.finishButtonText}>Kaydet ve Bitir</Text>
            </TouchableOpacity>
          )}
        </View>

        {/* Alt boşluk */}
        <View style={styles.bottomSpacing} />
      </ScrollView>
    </KeyboardAvoidingView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f0f4f8',
  },
  scrollContainer: {
    flex: 1,
    paddingHorizontal: 20,
    paddingBottom: 100,
  },
  header: {
    paddingTop: 50,
    paddingBottom: 30,
    paddingHorizontal: 0,
    alignItems: 'center',
    backgroundColor: '#667eea',
    borderBottomLeftRadius: 30,
    borderBottomRightRadius: 30,
    marginHorizontal: 0,
    marginBottom: 24,
  },
  headerContent: {
    alignItems: 'center',
    marginBottom: 12,
  },
  headerIcon: {
    fontSize: 48,
    marginBottom: 8,
  },
  title: {
    fontSize: 28,
    fontWeight: 'bold',
    color: 'white',
    marginBottom: 4,
    textAlign: 'center',
    textShadowColor: 'rgba(0, 0, 0, 0.3)',
    textShadowOffset: { width: 1, height: 1 },
    textShadowRadius: 2,
  },
  stageNumber: {
    fontSize: 16,
    color: 'rgba(255, 255, 255, 0.9)',
    fontWeight: '600',
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
    paddingHorizontal: 12,
    paddingVertical: 4,
    borderRadius: 12,
  },
  subtitle: {
    fontSize: 16,
    color: 'rgba(255, 255, 255, 0.9)',
    textAlign: 'center',
    lineHeight: 22,
  },
  bottomSpacing: {
    height: 200,
  },
  formContainer: {
    backgroundColor: 'white',
    margin: 20,
    borderRadius: 16,
    padding: 20,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 4,
  },
  inputGroup: {
    marginBottom: 20,
  },
  label: {
    fontSize: 16,
    fontWeight: '600',
    color: '#2d3748',
    marginBottom: 8,
  },
  input: {
    borderWidth: 1,
    borderColor: '#e2e8f0',
    borderRadius: 12,
    padding: 16,
    fontSize: 16,
    backgroundColor: '#f7fafc',
  },
  textArea: {
    borderWidth: 1,
    borderColor: '#e2e8f0',
    borderRadius: 12,
    padding: 16,
    fontSize: 16,
    backgroundColor: '#f7fafc',
  },
  questionInput: {
    minHeight: 100,
  },
  addButton: {
    backgroundColor: '#4299e1',
    borderRadius: 12,
    padding: 16,
    alignItems: 'center',
  },
  addButtonText: {
    color: 'white',
    fontSize: 16,
    fontWeight: '600',
  },
  previewContainer: {
    margin: 20,
    marginTop: 0,
  },
  previewTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#2d3748',
    marginBottom: 16,
  },
  stagePreview: {
    backgroundColor: 'white',
    borderRadius: 12,
    padding: 16,
    marginBottom: 12,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 1,
    },
    shadowOpacity: 0.05,
    shadowRadius: 4,
    elevation: 2,
  },
  stageHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 8,
  },
  stageTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#4299e1',
  },
  deleteButton: {
    padding: 4,
  },
  deleteButtonText: {
    fontSize: 16,
  },
  stageQuestion: {
    fontSize: 14,
    color: '#2d3748',
    marginBottom: 4,
  },
  stageAnswer: {
    fontSize: 14,
    color: '#48bb78',
    fontWeight: '600',
    marginBottom: 4,
  },
  stageHint: {
    fontSize: 14,
    color: '#718096',
    fontStyle: 'italic',
  },
  bottomButtons: {
    padding: 20,
    paddingTop: 0,
  },
  finishButton: {
    backgroundColor: '#48bb78',
    borderRadius: 12,
    padding: 16,
    alignItems: 'center',
  },
  finishButtonText: {
    color: 'white',
    fontSize: 16,
    fontWeight: '600',
  },
});

export default CreateQuestionStage;
