import React from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  StyleSheet,
  ScrollView,
} from 'react-native';

const CreatorPage = ({ navigation }) => {
  const handleTypeSelection = (type) => {
    if (type === 'question') {
      navigation.navigate('CreateQuestion', { stages: [], type: 'question' });
    } else {
      navigation.navigate('CreateMystery', { stages: [], type: 'mystery' });
    }
  };

  return (
    <ScrollView style={styles.container} contentContainerStyle={styles.contentContainer}>
      <View style={styles.header}>
        <View style={styles.headerContent}>
          <Text style={styles.headerIcon}>✨</Text>
          <Text style={styles.title}>Mektup Türünü Seçin</Text>
        </View>
        <Text style={styles.subtitle}>
          Sevdikleriniz için hangi tür bir mektup oluşturmak istiyorsunuz?
        </Text>
      </View>

      <View style={styles.optionsContainer}>
        <TouchableOpacity
          style={styles.optionCard}
          onPress={() => handleTypeSelection('question')}
        >
          <View style={styles.iconContainer}>
            <Text style={styles.icon}>🔐</Text>
          </View>
          <Text style={styles.optionTitle}>Şifreli Sorular</Text>
          <Text style={styles.optionDescription}>
            Bir dizi soru ve cevapla mektubunu koru. Her doğru cevap bir sonraki aşamayı açar.
          </Text>
          <View style={styles.features}>
            <Text style={styles.feature}>• Soru-cevap tabanlı</Text>
            <Text style={styles.feature}>• İpucu desteği</Text>
            <Text style={styles.feature}>• Kolay kurulum</Text>
          </View>
        </TouchableOpacity>

        <TouchableOpacity
          style={styles.optionCard}
          onPress={() => handleTypeSelection('mystery')}
        >
          <View style={styles.iconContainer}>
            <Text style={styles.icon}>🔍</Text>
          </View>
          <Text style={styles.optionTitle}>Dedektiflik Gizemi</Text>
          <Text style={styles.optionDescription}>
            Hikaye tabanlı bulmacalarla çözülmeyi bekleyen bir gizem yarat. Karakterler ve ipuçları ile.
          </Text>
          <View style={styles.features}>
            <Text style={styles.feature}>• Hikaye tabanlı</Text>
            <Text style={styles.feature}>• Karakter sistemi</Text>
            <Text style={styles.feature}>• Detektif deneyimi</Text>
          </View>
        </TouchableOpacity>
      </View>

      <View style={styles.tipContainer}>
        <Text style={styles.tipIcon}>💡</Text>
        <Text style={styles.tipText}>
          İpucu: Her iki türde de birden fazla aşama ekleyebilir ve son mektubunuzu özelleştirebilirsiniz.
        </Text>
      </View>

      {/* Alt boşluk */}
      <View style={styles.bottomSpacing} />
    </ScrollView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f8fafc',
  },
  contentContainer: {
    paddingHorizontal: 20,
    paddingBottom: 100,
  },
  header: {
    paddingTop: 40,
    paddingBottom: 30,
    paddingHorizontal: 20,
    alignItems: 'center',
    backgroundColor: '#667eea',
    borderBottomLeftRadius: 30,
    borderBottomRightRadius: 30,
    marginHorizontal: -20,
    marginBottom: 32,
  },
  headerContent: {
    alignItems: 'center',
    marginBottom: 12,
  },
  headerIcon: {
    fontSize: 48,
    marginBottom: 8,
  },
  title: {
    fontSize: 28,
    fontWeight: 'bold',
    color: 'white',
    marginBottom: 8,
    textAlign: 'center',
    textShadowColor: 'rgba(0, 0, 0, 0.3)',
    textShadowOffset: { width: 1, height: 1 },
    textShadowRadius: 2,
  },
  subtitle: {
    fontSize: 16,
    color: 'rgba(255, 255, 255, 0.9)',
    textAlign: 'center',
    lineHeight: 24,
  },
  bottomSpacing: {
    height: 200,
  },
  optionsContainer: {
    marginBottom: 32,
  },
  optionCard: {
    backgroundColor: 'white',
    borderRadius: 20,
    padding: 24,
    marginBottom: 20,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 4,
    },
    shadowOpacity: 0.1,
    shadowRadius: 12,
    elevation: 6,
  },
  iconContainer: {
    alignItems: 'center',
    marginBottom: 16,
  },
  icon: {
    fontSize: 48,
  },
  optionTitle: {
    fontSize: 22,
    fontWeight: 'bold',
    color: '#2d3748',
    textAlign: 'center',
    marginBottom: 12,
  },
  optionDescription: {
    fontSize: 16,
    color: '#4a5568',
    textAlign: 'center',
    lineHeight: 24,
    marginBottom: 16,
  },
  features: {
    alignItems: 'center',
  },
  feature: {
    fontSize: 14,
    color: '#718096',
    marginBottom: 4,
  },
  tipContainer: {
    flexDirection: 'row',
    backgroundColor: '#e6fffa',
    borderRadius: 12,
    padding: 16,
    alignItems: 'flex-start',
  },
  tipIcon: {
    fontSize: 20,
    marginRight: 12,
  },
  tipText: {
    flex: 1,
    fontSize: 14,
    color: '#2d3748',
    lineHeight: 20,
  },
});

export default CreatorPage;
