import React, { useState } from 'react';
import {
  View,
  Text,
  TextInput,
  TouchableOpacity,
  StyleSheet,
  ScrollView,
  Alert,
  KeyboardAvoidingView,
  Platform,
} from 'react-native';
import { Picker } from '@react-native-picker/picker';

const CreateMysteryStage = ({ navigation, route }) => {
  const { stages = [], type } = route.params;
  const [currentStage, setCurrentStage] = useState({
    story: '',
    characters: [{ name: '', statement: '' }],
    clues: [''],
    correctPerson: '',
  });

  const addCharacter = () => {
    setCurrentStage({
      ...currentStage,
      characters: [...currentStage.characters, { name: '', statement: '' }],
    });
  };

  const removeCharacter = (index) => {
    if (currentStage.characters.length > 1) {
      const newCharacters = currentStage.characters.filter((_, i) => i !== index);
      setCurrentStage({ ...currentStage, characters: newCharacters });
    }
  };

  const updateCharacter = (index, field, value) => {
    const newCharacters = [...currentStage.characters];
    newCharacters[index][field] = value;
    setCurrentStage({ ...currentStage, characters: newCharacters });
  };

  const addClue = () => {
    setCurrentStage({ ...currentStage, clues: [...currentStage.clues, ''] });
  };

  const removeClue = (index) => {
    if (currentStage.clues.length > 1) {
      const newClues = currentStage.clues.filter((_, i) => i !== index);
      setCurrentStage({ ...currentStage, clues: newClues });
    }
  };

  const updateClue = (index, value) => {
    const newClues = [...currentStage.clues];
    newClues[index] = value;
    setCurrentStage({ ...currentStage, clues: newClues });
  };

  const handleAddStage = () => {
    if (!currentStage.story.trim()) {
      return Alert.alert('Eksik Bilgi', 'Lütfen hikaye alanını doldurun.');
    }
    const validCharacters = currentStage.characters.filter(c => c.name.trim() && c.statement.trim());
    if (validCharacters.length < 2) {
      return Alert.alert('Eksik Bilgi', 'En az 2 geçerli karakter (isim ve ifade dolu) eklemelisiniz.');
    }
    const validClues = currentStage.clues.filter(c => c.trim());
    if (validClues.length === 0) {
      return Alert.alert('Eksik Bilgi', 'En az bir geçerli ipucu eklemelisiniz.');
    }
    if (!currentStage.correctPerson.trim()) {
      return Alert.alert('Eksik Bilgi', 'Lütfen doğru kişiyi seçin.');
    }
    if (!validCharacters.find(c => c.name === currentStage.correctPerson)) {
        return Alert.alert('Hata', 'Seçilen suçlu, geçerli karakterler listesinde bulunmuyor.');
    }

    const newStages = [...stages, { ...currentStage, characters: validCharacters, clues: validClues, type: 'mystery' }];
    setCurrentStage({ story: '', characters: [{ name: '', statement: '' }], clues: [''], correctPerson: '' });
    navigation.setParams({ stages: newStages });
  };

  const handleRemoveStage = (index) => {
    Alert.alert('Aşamayı Sil', 'Bu aşamayı silmek istediğinizden emin misiniz?', [
      { text: 'İptal', style: 'cancel' },
      { text: 'Sil', style: 'destructive', onPress: () => {
          const newStages = stages.filter((_, i) => i !== index);
          navigation.setParams({ stages: newStages });
      }},
    ]);
  };

  const handleFinish = () => {
    if (stages.length === 0) {
      return Alert.alert('Eksik Bilgi', 'En az bir aşama eklemelisiniz.');
    }
    navigation.navigate('FinalEditor', { stages, type });
  };

  return (
    <KeyboardAvoidingView style={styles.container} behavior={Platform.OS === 'ios' ? 'padding' : 'height'}>
      <ScrollView style={styles.scrollContainer} contentContainerStyle={styles.contentContainer} showsVerticalScrollIndicator={false}>
        <View style={styles.header}>
          <Text style={styles.headerIcon}>🔍</Text>
          <Text style={styles.title}>Gizem Aşaması Ekle</Text>
          <Text style={styles.subtitle}>Aşama {stages.length + 1}</Text>
        </View>

        <View style={styles.card}>
          <Text style={styles.cardTitle}>📖 Hikaye</Text>
          <TextInput
            style={styles.textArea}
            placeholder="Olayın geçtiği yer, zaman ve ana kurguyu yazın..."
            value={currentStage.story}
            onChangeText={(text) => setCurrentStage({ ...currentStage, story: text })}
            multiline
          />
        </View>

        <View style={styles.card}>
          <View style={styles.cardHeader}>
            <Text style={styles.cardTitle}>👥 Karakterler</Text>
            <TouchableOpacity style={styles.addButton} onPress={addCharacter}><Text style={styles.addButtonText}>+ Ekle</Text></TouchableOpacity>
          </View>
          {currentStage.characters.map((char, index) => (
            <View key={index} style={styles.itemContainer}>
              <TextInput
                style={styles.input}
                placeholder={`Karakter ${index + 1} Adı`}
                value={char.name}
                onChangeText={(text) => updateCharacter(index, 'name', text)}
              />
              <TextInput
                style={styles.textAreaSmall}
                placeholder={`Karakter ${index + 1} İfadesi`}
                value={char.statement}
                onChangeText={(text) => updateCharacter(index, 'statement', text)}
                multiline
              />
              {currentStage.characters.length > 1 && (
                <TouchableOpacity style={styles.removeButton} onPress={() => removeCharacter(index)}><Text style={styles.removeButtonText}>Karakteri Sil</Text></TouchableOpacity>
              )}
            </View>
          ))}
        </View>

        <View style={styles.card}>
          <View style={styles.cardHeader}>
            <Text style={styles.cardTitle}>💡 İpuçları</Text>
            <TouchableOpacity style={styles.addButton} onPress={addClue}><Text style={styles.addButtonText}>+ Ekle</Text></TouchableOpacity>
          </View>
          {currentStage.clues.map((clue, index) => (
            <View key={index} style={styles.itemContainerRow}>
              <TextInput
                style={[styles.input, {flex: 1}] }
                placeholder={`İpucu ${index + 1}`}
                value={clue}
                onChangeText={(text) => updateClue(index, text)}
              />
              {currentStage.clues.length > 1 && (
                <TouchableOpacity style={styles.removeButtonSmall} onPress={() => removeClue(index)}><Text style={styles.removeButtonText}>Sil</Text></TouchableOpacity>
              )}
            </View>
          ))}
        </View>

        <View style={styles.card}>
          <Text style={styles.cardTitle}>🎯 Suçlu Kim?</Text>
          <View style={styles.pickerContainer}>
            <Picker
              selectedValue={currentStage.correctPerson}
              onValueChange={(itemValue) => setCurrentStage({ ...currentStage, correctPerson: itemValue })}
            >
              <Picker.Item label="Suçluyu seçin..." value="" />
              {currentStage.characters.filter(c => c.name.trim()).map((char, index) => (
                <Picker.Item key={index} label={char.name} value={char.name} />
              ))}
            </Picker>
          </View>
        </View>

        <TouchableOpacity style={styles.submitButton} onPress={handleAddStage}><Text style={styles.submitButtonText}>Aşamayı Ekle</Text></TouchableOpacity>

        {stages.length > 0 && (
          <View style={styles.previewContainer}>
            <Text style={styles.previewTitle}>Eklenen Aşamalar ({stages.length})</Text>
            {stages.map((stage, index) => (
                <View key={index} style={styles.stagePreview}>
                    <View style={{flex: 1}}>
                        <Text style={styles.stageTitle}>Aşama {index + 1}</Text>
                        <Text style={styles.stageStory} numberOfLines={2}>{stage.story}</Text>
                    </View>
                    <TouchableOpacity style={styles.deleteButton} onPress={() => handleRemoveStage(index)}><Text style={styles.deleteButtonText}>🗑️</Text></TouchableOpacity>
                </View>
            ))}
            <TouchableOpacity style={styles.finishButton} onPress={handleFinish}><Text style={styles.finishButtonText}>Kaydet ve Bitir</Text></TouchableOpacity>
          </View>
        )}
      </ScrollView>
    </KeyboardAvoidingView>
  );
};

const styles = StyleSheet.create({
  container: { flex: 1, backgroundColor: '#f0f4f8' },
  scrollContainer: { flex: 1 },
  contentContainer: { paddingHorizontal: 20, paddingBottom: 120 },
  header: {
    paddingTop: 40,
    paddingBottom: 30,
    paddingHorizontal: 20,
    alignItems: 'center',
    backgroundColor: '#8b5cf6',
    borderBottomLeftRadius: 30,
    borderBottomRightRadius: 30,
    marginBottom: 24,
    marginHorizontal: -20,
  },
  headerIcon: { fontSize: 48, marginBottom: 8 },
  title: { fontSize: 28, fontWeight: 'bold', color: 'white', textAlign: 'center' },
  subtitle: { fontSize: 16, color: 'rgba(255, 255, 255, 0.9)', textAlign: 'center', marginTop: 8 },
  card: {
    backgroundColor: 'white',
    borderRadius: 16,
    padding: 20,
    marginBottom: 20,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.08,
    shadowRadius: 8,
    elevation: 3,
  },
  cardHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 16,
  },
  cardTitle: { fontSize: 20, fontWeight: 'bold', color: '#1e293b' },
  input: { borderWidth: 1, borderColor: '#e2e8f0', borderRadius: 12, padding: 14, fontSize: 16, backgroundColor: '#f8fafc' },
  textArea: { minHeight: 120, textAlignVertical: 'top', borderWidth: 1, borderColor: '#e2e8f0', borderRadius: 12, padding: 14, fontSize: 16, backgroundColor: '#f8fafc' },
  textAreaSmall: { minHeight: 80, textAlignVertical: 'top', borderWidth: 1, borderColor: '#e2e8f0', borderRadius: 12, padding: 14, fontSize: 16, backgroundColor: '#f8fafc', marginTop: 8 },
  addButton: { backgroundColor: '#ede9fe', paddingHorizontal: 14, paddingVertical: 8, borderRadius: 10 },
  addButtonText: { color: '#7c3aed', fontWeight: 'bold' },
  itemContainer: { marginBottom: 16, padding: 16, borderWidth: 1, borderColor: '#f3f4f6', borderRadius: 12, backgroundColor: '#f9fafb' },
  itemContainerRow: { flexDirection: 'row', alignItems: 'center', marginBottom: 10, gap: 10 },
  removeButton: { backgroundColor: '#fee2e2', padding: 10, borderRadius: 8, alignItems: 'center', marginTop: 12 },
  removeButtonSmall: { backgroundColor: '#fee2e2', padding: 12, borderRadius: 8, alignItems: 'center', justifyContent: 'center' },
  removeButtonText: { color: '#ef4444', fontWeight: 'bold', fontSize: 12 },
  pickerContainer: { borderWidth: 1, borderColor: '#e2e8f0', borderRadius: 12, backgroundColor: '#f8fafc', overflow: 'hidden' },
  submitButton: { backgroundColor: '#8b5cf6', borderRadius: 12, padding: 18, alignItems: 'center', marginTop: 8 },
  submitButtonText: { color: 'white', fontSize: 18, fontWeight: 'bold' },
  previewContainer: { marginTop: 20 },
  previewTitle: { fontSize: 20, fontWeight: 'bold', color: '#1e293b', marginBottom: 16 },
  stagePreview: {
    backgroundColor: 'white',
    borderRadius: 12,
    padding: 16,
    marginBottom: 12,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  stageTitle: { fontSize: 16, fontWeight: 'bold', color: '#4c1d95' },
  stageStory: { fontSize: 14, color: '#475569', marginTop: 4 },
  deleteButton: { padding: 10, backgroundColor: '#fee2e2', borderRadius: 10 },
  deleteButtonText: { fontSize: 18 },
  finishButton: { backgroundColor: '#22c55e', borderRadius: 12, padding: 18, alignItems: 'center', marginTop: 16 },
  finishButtonText: { color: 'white', fontSize: 18, fontWeight: 'bold' },
});

export default CreateMysteryStage;
