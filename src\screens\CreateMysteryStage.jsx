import React, { useState } from 'react';
import {
  View,
  Text,
  TextInput,
  TouchableOpacity,
  StyleSheet,
  ScrollView,
  Alert,
  KeyboardAvoidingView,
  Platform,
  Modal,
} from 'react-native';
import { Picker } from '@react-native-picker/picker';

const CreateMysteryStage = ({ navigation, route }) => {
  const { stages = [], type } = route.params;
  const [currentStage, setCurrentStage] = useState({
    story: '',
    characters: [{ name: '', statement: '' }],
    clues: [''],
    correctPerson: '',
  });

  // Modal states
  const [storyModalVisible, setStoryModalVisible] = useState(false);
  const [characterModalVisible, setCharacterModalVisible] = useState(false);
  const [clueModalVisible, setClueModalVisible] = useState(false);
  const [editingCharacterIndex, setEditingCharacterIndex] = useState(-1);
  const [editingClueIndex, setEditingClueIndex] = useState(-1);
  const [tempStory, setTempStory] = useState('');
  const [tempCharacter, setTempCharacter] = useState({ name: '', statement: '' });
  const [tempClue, setTempClue] = useState('');

  const addCharacter = () => {
    setCurrentStage({
      ...currentStage,
      characters: [...currentStage.characters, { name: '', statement: '' }],
    });
  };

  const removeCharacter = (index) => {
    if (currentStage.characters.length > 1) {
      const newCharacters = currentStage.characters.filter((_, i) => i !== index);
      setCurrentStage({ ...currentStage, characters: newCharacters });
    }
  };

  const updateCharacter = (index, field, value) => {
    const newCharacters = [...currentStage.characters];
    newCharacters[index][field] = value;
    setCurrentStage({ ...currentStage, characters: newCharacters });
  };

  const addClue = () => {
    setCurrentStage({
      ...currentStage,
      clues: [...currentStage.clues, ''],
    });
  };

  const removeClue = (index) => {
    if (currentStage.clues.length > 1) {
      const newClues = currentStage.clues.filter((_, i) => i !== index);
      setCurrentStage({ ...currentStage, clues: newClues });
    }
  };

  const updateClue = (index, value) => {
    const newClues = [...currentStage.clues];
    newClues[index] = value;
    setCurrentStage({ ...currentStage, clues: newClues });
  };

  const handleAddStage = () => {
    if (!currentStage.story.trim()) {
      Alert.alert('Eksik Bilgi', 'Lütfen hikaye alanını doldurun.');
      return;
    }

    const validCharacters = currentStage.characters.filter(
      char => char.name.trim() && char.statement.trim()
    );

    if (validCharacters.length < 2) {
      Alert.alert('Eksik Bilgi', 'En az 2 karakter eklemelisiniz.');
      return;
    }

    const validClues = currentStage.clues.filter(clue => clue.trim());

    if (validClues.length === 0) {
      Alert.alert('Eksik Bilgi', 'En az bir ipucu eklemelisiniz.');
      return;
    }

    if (!currentStage.correctPerson.trim()) {
      Alert.alert('Eksik Bilgi', 'Lütfen doğru kişiyi seçin.');
      return;
    }

    const characterNames = validCharacters.map(char => char.name);
    if (!characterNames.includes(currentStage.correctPerson)) {
      Alert.alert('Hata', 'Doğru kişi karakter listesinde bulunmuyor.');
      return;
    }

    const newStages = [...stages, {
      ...currentStage,
      characters: validCharacters,
      clues: validClues,
      type: 'mystery'
    }];
    
    // Yeni boş aşama formu
    setCurrentStage({
      story: '',
      characters: [{ name: '', statement: '' }],
      clues: [''],
      correctPerson: '',
    });

    navigation.setParams({ stages: newStages });
  };

  const handleRemoveStage = (index) => {
    Alert.alert(
      'Aşamayı Sil',
      'Bu aşamayı silmek istediğinizden emin misiniz?',
      [
        { text: 'İptal', style: 'cancel' },
        {
          text: 'Sil',
          style: 'destructive',
          onPress: () => {
            const newStages = stages.filter((_, i) => i !== index);
            navigation.setParams({ stages: newStages });
          },
        },
      ]
    );
  };

  const handleFinish = () => {
    if (stages.length === 0) {
      Alert.alert('Eksik Bilgi', 'En az bir aşama eklemelisiniz.');
      return;
    }

    navigation.navigate('FinalEditor', { stages, type });
  };

  // Modal handlers
  const saveStory = () => {
    setCurrentStage({ ...currentStage, story: tempStory });
    setStoryModalVisible(false);
  };

  const saveCharacter = () => {
    const newCharacters = [...currentStage.characters];
    newCharacters[editingCharacterIndex] = tempCharacter;
    setCurrentStage({ ...currentStage, characters: newCharacters });
    setCharacterModalVisible(false);
  };

  const saveClue = () => {
    const newClues = [...currentStage.clues];
    newClues[editingClueIndex] = tempClue;
    setCurrentStage({ ...currentStage, clues: newClues });
    setClueModalVisible(false);
  };

  const renderCharacterInput = (character, index) => (
    <View key={index} style={styles.characterCard}>
      <View style={styles.characterHeader}>
        <View style={styles.characterTitleContainer}>
          <Text style={styles.characterIcon}>👤</Text>
          <Text style={styles.characterTitle}>Karakter {index + 1}</Text>
        </View>
        {currentStage.characters.length > 1 && (
          <TouchableOpacity
            style={styles.removeButton}
            onPress={() => removeCharacter(index)}
          >
            <Text style={styles.removeButtonText}>❌</Text>
          </TouchableOpacity>
        )}
      </View>
      <View style={styles.characterInputs}>
        <TouchableOpacity
          style={styles.characterEditButton}
          onPress={() => {
            setTempCharacter({ name: character.name, statement: character.statement });
            setEditingCharacterIndex(index);
            setCharacterModalVisible(true);
          }}
        >
          <View style={styles.characterPreview}>
            <Text style={styles.characterPreviewName}>
              {character.name || 'Karakter adı girilmedi'}
            </Text>
            <Text style={styles.characterPreviewStatement} numberOfLines={2}>
              {character.statement || 'İfade girilmedi'}
            </Text>
          </View>
          <Text style={styles.editIcon}>✏️</Text>
        </TouchableOpacity>
      </View>
    </View>
  );

  const renderClueInput = (clue, index) => (
    <View key={index} style={styles.clueCard}>
      <View style={styles.clueHeader}>
        <Text style={styles.clueIcon}>💡</Text>
        <Text style={styles.clueTitle}>İpucu {index + 1}</Text>
        {currentStage.clues.length > 1 && (
          <TouchableOpacity
            style={styles.removeButton}
            onPress={() => removeClue(index)}
          >
            <Text style={styles.removeButtonText}>❌</Text>
          </TouchableOpacity>
        )}
      </View>
      <TouchableOpacity
        style={styles.clueEditButton}
        onPress={() => {
          setTempClue(clue);
          setEditingClueIndex(index);
          setClueModalVisible(true);
        }}
      >
        <Text style={styles.cluePreviewText} numberOfLines={2}>
          {clue || 'İpucu yazmak için tıklayın...'}
        </Text>
        <Text style={styles.editIcon}>✏️</Text>
      </TouchableOpacity>
    </View>
  );

  const renderStagePreview = (stage, index) => (
    <View key={index} style={styles.stagePreview}>
      <View style={styles.stageHeader}>
        <Text style={styles.stageTitle}>Gizem Aşaması {index + 1}</Text>
        <TouchableOpacity
          style={styles.deleteButton}
          onPress={() => handleRemoveStage(index)}
        >
          <Text style={styles.deleteButtonText}>🗑️</Text>
        </TouchableOpacity>
      </View>
      <Text style={styles.stageStory} numberOfLines={3}>
        {stage.story}
      </Text>
      <Text style={styles.stageInfo}>
        {stage.characters.length} karakter, {stage.clues.length} ipucu
      </Text>
      <Text style={styles.stageAnswer}>Doğru kişi: {stage.correctPerson}</Text>
    </View>
  );

  return (
    <KeyboardAvoidingView
      style={styles.container}
      behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
    >
      <ScrollView style={styles.scrollContainer} showsVerticalScrollIndicator={false}>
        <View style={styles.header}>
          <View style={styles.headerContent}>
            <Text style={styles.headerIcon}>🔍</Text>
            <Text style={styles.title}>Gizem Senaryosu Ekle</Text>
            <Text style={styles.stageNumber}>Aşama {stages.length + 1}</Text>
          </View>
          <Text style={styles.subtitle}>
            Hikaye tabanlı bir gizem bulmacası oluşturun
          </Text>
        </View>

        <View style={styles.formContainer}>
          {/* Gizemin Hikayesi Bölümü */}
          <View style={styles.sectionContainer}>
            <View style={styles.sectionHeader}>
              <Text style={styles.sectionIcon}>📖</Text>
              <Text style={styles.sectionTitle}>Gizemin Hikayesi</Text>
            </View>
            <Text style={styles.sectionDescription}>
              Olayın geçtiği yer, zaman ve ana kurguyu yazın
            </Text>
            <TouchableOpacity
              style={styles.textAreaButton}
              onPress={() => {
                setTempStory(currentStage.story);
                setStoryModalVisible(true);
              }}
            >
              <Text style={styles.textAreaButtonText}>
                {currentStage.story || 'Hikayeyi yazmak için tıklayın...'}
              </Text>
              <Text style={styles.editIcon}>✏️</Text>
            </TouchableOpacity>
          </View>

          {/* Karakterler ve İfadeleri Bölümü */}
          <View style={styles.sectionContainer}>
            <View style={styles.sectionHeader}>
              <Text style={styles.sectionIcon}>👥</Text>
              <Text style={styles.sectionTitle}>Karakterler ve İfadeleri</Text>
              <TouchableOpacity style={styles.addButton} onPress={addCharacter}>
                <Text style={styles.addButtonText}>+ Karakter Ekle</Text>
              </TouchableOpacity>
            </View>
            <Text style={styles.sectionDescription}>
              Her karakter için ad ve ifade/kanıt bilgisi ekleyin
            </Text>
            {currentStage.characters.map(renderCharacterInput)}
          </View>

          {/* İpuçları Bölümü */}
          <View style={styles.sectionContainer}>
            <View style={styles.sectionHeader}>
              <Text style={styles.sectionIcon}>💡</Text>
              <Text style={styles.sectionTitle}>İpuçları</Text>
              <TouchableOpacity style={styles.addButton} onPress={addClue}>
                <Text style={styles.addButtonText}>+ İpucu Ekle</Text>
              </TouchableOpacity>
            </View>
            <Text style={styles.sectionDescription}>
              Gizemi çözmek için gerekli ipuçlarını ekleyin
            </Text>
            {currentStage.clues.map(renderClueInput)}
          </View>

          {/* Doğru Kişi Seçimi Bölümü */}
          <View style={styles.sectionContainer}>
            <View style={styles.sectionHeader}>
              <Text style={styles.sectionIcon}>🎯</Text>
              <Text style={styles.sectionTitle}>Doğru Kişi Kim?</Text>
            </View>
            <Text style={styles.sectionDescription}>
              Yukarıda eklediğiniz karakterlerden suçluyu seçin
            </Text>
            <View style={styles.pickerContainer}>
              <Picker
                selectedValue={currentStage.correctPerson}
                style={styles.picker}
                onValueChange={(itemValue) => setCurrentStage({ ...currentStage, correctPerson: itemValue })}
              >
                <Picker.Item label="Suçluyu seçin..." value="" />
                {currentStage.characters
                  .filter(char => char.name.trim())
                  .map((character, index) => (
                    <Picker.Item
                      key={index}
                      label={character.name}
                      value={character.name}
                    />
                  ))
                }
              </Picker>
            </View>
          </View>

          <TouchableOpacity style={styles.submitButton} onPress={handleAddStage}>
            <Text style={styles.submitButtonText}>🔍 Gizem Aşaması Ekle</Text>
          </TouchableOpacity>
        </View>

        {stages.length > 0 && (
          <View style={styles.previewContainer}>
            <Text style={styles.previewTitle}>Eklenen Aşamalar ({stages.length})</Text>
            {stages.map(renderStagePreview)}
          </View>
        )}

        <View style={styles.bottomButtons}>
          {stages.length > 0 && (
            <TouchableOpacity style={styles.finishButton} onPress={handleFinish}>
              <Text style={styles.finishButtonText}>Kaydet ve Bitir</Text>
            </TouchableOpacity>
          )}
        </View>

        {/* Alt boşluk */}
        <View style={styles.bottomSpacing} />
      </ScrollView>

      {/* Story Modal */}
      <Modal
        animationType="slide"
        transparent={true}
        visible={storyModalVisible}
        onRequestClose={() => setStoryModalVisible(false)}
      >
        <View style={styles.modalOverlay}>
          <View style={styles.modalContainer}>
            <View style={styles.modalHeader}>
              <Text style={styles.modalTitle}>📖 Hikaye Yazın</Text>
              <TouchableOpacity onPress={() => setStoryModalVisible(false)}>
                <Text style={styles.closeButton}>✕</Text>
              </TouchableOpacity>
            </View>
            <TextInput
              style={styles.modalTextArea}
              placeholder="Olayın geçtiği yer, zaman ve ana kurguyu yazın..."
              value={tempStory}
              onChangeText={setTempStory}
              multiline
              numberOfLines={8}
              textAlignVertical="top"
            />
            <View style={styles.modalButtons}>
              <TouchableOpacity style={styles.modalCancelButton} onPress={() => setStoryModalVisible(false)}>
                <Text style={styles.modalCancelText}>İptal</Text>
              </TouchableOpacity>
              <TouchableOpacity style={styles.modalSaveButton} onPress={saveStory}>
                <Text style={styles.modalSaveText}>Kaydet</Text>
              </TouchableOpacity>
            </View>
          </View>
        </View>
      </Modal>

      {/* Character Modal */}
      <Modal
        animationType="slide"
        transparent={true}
        visible={characterModalVisible}
        onRequestClose={() => setCharacterModalVisible(false)}
      >
        <View style={styles.modalOverlay}>
          <View style={styles.modalContainer}>
            <View style={styles.modalHeader}>
              <Text style={styles.modalTitle}>👤 Karakter Düzenle</Text>
              <TouchableOpacity onPress={() => setCharacterModalVisible(false)}>
                <Text style={styles.closeButton}>✕</Text>
              </TouchableOpacity>
            </View>
            <TextInput
              style={styles.modalInput}
              placeholder="Karakter Adı (örn: Ayşe)"
              value={tempCharacter.name}
              onChangeText={(text) => setTempCharacter({ ...tempCharacter, name: text })}
            />
            <TextInput
              style={styles.modalTextArea}
              placeholder="Karakterin ifadesi/kanıtı (örn: 'Dün gece erkenden yatmıştım.')"
              value={tempCharacter.statement}
              onChangeText={(text) => setTempCharacter({ ...tempCharacter, statement: text })}
              multiline
              numberOfLines={6}
              textAlignVertical="top"
            />
            <View style={styles.modalButtons}>
              <TouchableOpacity style={styles.modalCancelButton} onPress={() => setCharacterModalVisible(false)}>
                <Text style={styles.modalCancelText}>İptal</Text>
              </TouchableOpacity>
              <TouchableOpacity style={styles.modalSaveButton} onPress={saveCharacter}>
                <Text style={styles.modalSaveText}>Kaydet</Text>
              </TouchableOpacity>
            </View>
          </View>
        </View>
      </Modal>

      {/* Clue Modal */}
      <Modal
        animationType="slide"
        transparent={true}
        visible={clueModalVisible}
        onRequestClose={() => setClueModalVisible(false)}
      >
        <View style={styles.modalOverlay}>
          <View style={styles.modalContainer}>
            <View style={styles.modalHeader}>
              <Text style={styles.modalTitle}>💡 İpucu Yazın</Text>
              <TouchableOpacity onPress={() => setClueModalVisible(false)}>
                <Text style={styles.closeButton}>✕</Text>
              </TouchableOpacity>
            </View>
            <TextInput
              style={styles.modalTextArea}
              placeholder="İpucu yazın (örn: Bahçede kırmızı bir gül yaprağı bulundu...)"
              value={tempClue}
              onChangeText={setTempClue}
              multiline
              numberOfLines={4}
              textAlignVertical="top"
            />
            <View style={styles.modalButtons}>
              <TouchableOpacity style={styles.modalCancelButton} onPress={() => setClueModalVisible(false)}>
                <Text style={styles.modalCancelText}>İptal</Text>
              </TouchableOpacity>
              <TouchableOpacity style={styles.modalSaveButton} onPress={saveClue}>
                <Text style={styles.modalSaveText}>Kaydet</Text>
              </TouchableOpacity>
            </View>
          </View>
        </View>
      </Modal>
    </KeyboardAvoidingView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f7fafc',
  },
  scrollContainer: {
    flex: 1,
  },
  header: {
    padding: 24,
    alignItems: 'center',
    backgroundColor: '#667eea',
    borderBottomLeftRadius: 30,
    borderBottomRightRadius: 30,
  },
  headerContent: {
    alignItems: 'center',
    marginBottom: 12,
  },
  headerIcon: {
    fontSize: 48,
    marginBottom: 8,
  },
  title: {
    fontSize: 28,
    fontWeight: 'bold',
    color: 'white',
    marginBottom: 4,
    textAlign: 'center',
    textShadowColor: 'rgba(0, 0, 0, 0.3)',
    textShadowOffset: { width: 1, height: 1 },
    textShadowRadius: 2,
  },
  stageNumber: {
    fontSize: 16,
    color: 'rgba(255, 255, 255, 0.9)',
    fontWeight: '600',
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
    paddingHorizontal: 12,
    paddingVertical: 4,
    borderRadius: 12,
  },
  subtitle: {
    fontSize: 16,
    color: 'rgba(255, 255, 255, 0.9)',
    textAlign: 'center',
    lineHeight: 22,
  },
  formContainer: {
    backgroundColor: 'white',
    margin: 20,
    borderRadius: 24,
    padding: 24,
    shadowColor: '#667eea',
    shadowOffset: {
      width: 0,
      height: 8,
    },
    shadowOpacity: 0.15,
    shadowRadius: 16,
    elevation: 8,
    borderWidth: 1,
    borderColor: '#e2e8f0',
  },
  sectionContainer: {
    marginBottom: 32,
  },
  sectionHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 12,
    justifyContent: 'space-between',
  },
  sectionIcon: {
    fontSize: 24,
    marginRight: 12,
  },
  sectionTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#1a202c',
    flex: 1,
  },
  sectionDescription: {
    fontSize: 14,
    color: '#6b7280',
    marginBottom: 16,
    fontStyle: 'italic',
  },
  addButton: {
    backgroundColor: '#667eea',
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 20,
    shadowColor: '#667eea',
    shadowOffset: {
      width: 0,
      height: 4,
    },
    shadowOpacity: 0.3,
    shadowRadius: 8,
    elevation: 4,
  },
  addButtonText: {
    color: 'white',
    fontSize: 14,
    fontWeight: 'bold',
  },

  input: {
    borderWidth: 2,
    borderColor: '#e2e8f0',
    borderRadius: 16,
    padding: 18,
    fontSize: 16,
    backgroundColor: '#f8fafc',
    marginBottom: 12,
    shadowColor: '#667eea',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
  },
  textArea: {
    borderWidth: 2,
    borderColor: '#e2e8f0',
    borderRadius: 16,
    padding: 18,
    fontSize: 16,
    backgroundColor: '#f8fafc',
    shadowColor: '#667eea',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
  },
  storyInput: {
    minHeight: 120,
  },
  statementInput: {
    minHeight: 80,
  },
  characterCard: {
    backgroundColor: '#ffecd2',
    borderRadius: 20,
    padding: 20,
    marginBottom: 16,
    borderWidth: 2,
    borderColor: '#fed7aa',
    shadowColor: '#f97316',
    shadowOffset: {
      width: 0,
      height: 4,
    },
    shadowOpacity: 0.2,
    shadowRadius: 8,
    elevation: 4,
  },
  characterHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 16,
  },
  characterTitleContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  characterIcon: {
    fontSize: 20,
    marginRight: 8,
  },
  characterTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#c2410c',
  },
  characterInputs: {
    gap: 12,
  },
  characterNameInput: {
    borderWidth: 2,
    borderColor: '#fed7aa',
    borderRadius: 12,
    padding: 16,
    fontSize: 16,
    backgroundColor: 'white',
    fontWeight: '600',
  },
  removeButton: {
    padding: 4,
  },
  removeButtonText: {
    fontSize: 16,
  },
  clueCard: {
    backgroundColor: '#a8edea',
    borderRadius: 16,
    padding: 16,
    marginBottom: 12,
    borderWidth: 2,
    borderColor: '#7dd3fc',
    shadowColor: '#0ea5e9',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
  },
  clueHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 12,
  },
  clueIcon: {
    fontSize: 18,
    marginRight: 8,
  },
  clueTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#0c4a6e',
    flex: 1,
  },
  clueInput: {
    borderWidth: 2,
    borderColor: '#7dd3fc',
    borderRadius: 12,
    padding: 14,
    fontSize: 15,
    backgroundColor: 'white',
    minHeight: 60,
  },
  pickerContainer: {
    borderWidth: 2,
    borderColor: '#e2e8f0',
    borderRadius: 16,
    backgroundColor: '#f8fafc',
    shadowColor: '#667eea',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
    overflow: 'hidden',
  },
  picker: {
    height: 50,
    width: '100%',
    color: '#1a202c',
  },
  submitButton: {
    backgroundColor: '#667eea',
    borderRadius: 20,
    padding: 18,
    alignItems: 'center',
    shadowColor: '#667eea',
    shadowOffset: {
      width: 0,
      height: 6,
    },
    shadowOpacity: 0.3,
    shadowRadius: 12,
    elevation: 6,
    marginTop: 8,
  },
  submitButtonText: {
    color: 'white',
    fontSize: 18,
    fontWeight: 'bold',
    textShadowColor: 'rgba(0, 0, 0, 0.3)',
    textShadowOffset: { width: 1, height: 1 },
    textShadowRadius: 2,
  },
  removeButton: {
    backgroundColor: '#fecaca',
    borderRadius: 12,
    padding: 8,
    shadowColor: '#ef4444',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.2,
    shadowRadius: 4,
    elevation: 2,
  },
  removeButtonText: {
    fontSize: 14,
  },
  previewContainer: {
    margin: 20,
    marginTop: 0,
  },
  previewTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#1a202c',
    marginBottom: 20,
    textAlign: 'center',
  },
  stagePreview: {
    backgroundColor: 'linear-gradient(135deg, #ffecd2 0%, #fcb69f 100%)',
    borderRadius: 20,
    padding: 20,
    marginBottom: 16,
    shadowColor: '#f97316',
    shadowOffset: {
      width: 0,
      height: 6,
    },
    shadowOpacity: 0.2,
    shadowRadius: 12,
    elevation: 6,
    borderWidth: 2,
    borderColor: '#fed7aa',
  },
  stageHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 8,
  },
  stageTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#c2410c',
    textShadowColor: 'rgba(255, 255, 255, 0.5)',
    textShadowOffset: { width: 1, height: 1 },
    textShadowRadius: 2,
  },
  deleteButton: {
    padding: 8,
    backgroundColor: '#fecaca',
    borderRadius: 12,
  },
  deleteButtonText: {
    fontSize: 16,
  },
  stageStory: {
    fontSize: 15,
    color: '#1a202c',
    marginBottom: 12,
    lineHeight: 22,
    fontWeight: '500',
  },
  stageInfo: {
    fontSize: 14,
    color: '#4a5568',
    marginBottom: 8,
    fontWeight: '600',
  },
  stageAnswer: {
    fontSize: 15,
    color: '#059669',
    fontWeight: 'bold',
    backgroundColor: 'rgba(255, 255, 255, 0.7)',
    padding: 8,
    borderRadius: 12,
    textAlign: 'center',
  },
  bottomButtons: {
    padding: 20,
    paddingTop: 0,
  },
  finishButton: {
    backgroundColor: 'linear-gradient(135deg, #10b981 0%, #059669 100%)',
    borderRadius: 20,
    padding: 18,
    alignItems: 'center',
    shadowColor: '#10b981',
    shadowOffset: {
      width: 0,
      height: 6,
    },
    shadowOpacity: 0.3,
    shadowRadius: 12,
    elevation: 6,
  },
  finishButtonText: {
    color: 'white',
    fontSize: 18,
    fontWeight: 'bold',
    textShadowColor: 'rgba(0, 0, 0, 0.3)',
    textShadowOffset: { width: 1, height: 1 },
    textShadowRadius: 2,
  },
  bottomSpacing: {
    height: 200, // Yarım sayfa boşluk
  },
  // Button styles for popup triggers
  textAreaButton: {
    borderWidth: 2,
    borderColor: '#e2e8f0',
    borderRadius: 16,
    padding: 18,
    backgroundColor: '#f8fafc',
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    minHeight: 80,
  },
  textAreaButtonText: {
    flex: 1,
    fontSize: 16,
    color: '#4a5568',
    lineHeight: 22,
  },
  characterEditButton: {
    borderWidth: 2,
    borderColor: '#fed7aa',
    borderRadius: 12,
    padding: 16,
    backgroundColor: 'white',
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  characterPreview: {
    flex: 1,
  },
  characterPreviewName: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#c2410c',
    marginBottom: 4,
  },
  characterPreviewStatement: {
    fontSize: 14,
    color: '#4a5568',
    lineHeight: 20,
  },
  clueEditButton: {
    borderWidth: 2,
    borderColor: '#7dd3fc',
    borderRadius: 12,
    padding: 14,
    backgroundColor: 'white',
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    minHeight: 60,
  },
  cluePreviewText: {
    flex: 1,
    fontSize: 15,
    color: '#4a5568',
    lineHeight: 20,
  },
  editIcon: {
    fontSize: 20,
    marginLeft: 12,
  },
  // Modal styles
  modalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
  },
  modalContainer: {
    backgroundColor: 'white',
    borderRadius: 20,
    padding: 24,
    width: '100%',
    maxHeight: '80%',
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 10,
    },
    shadowOpacity: 0.25,
    shadowRadius: 20,
    elevation: 10,
  },
  modalHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 20,
    paddingBottom: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#e2e8f0',
  },
  modalTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#1a202c',
  },
  closeButton: {
    fontSize: 24,
    color: '#718096',
    fontWeight: 'bold',
  },
  modalInput: {
    borderWidth: 2,
    borderColor: '#e2e8f0',
    borderRadius: 12,
    padding: 16,
    fontSize: 16,
    marginBottom: 16,
    backgroundColor: '#f8fafc',
  },
  modalTextArea: {
    borderWidth: 2,
    borderColor: '#e2e8f0',
    borderRadius: 12,
    padding: 16,
    fontSize: 16,
    backgroundColor: '#f8fafc',
    minHeight: 120,
    textAlignVertical: 'top',
  },
  modalButtons: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginTop: 20,
    gap: 12,
  },
  modalCancelButton: {
    flex: 1,
    backgroundColor: '#f7fafc',
    borderRadius: 12,
    padding: 16,
    alignItems: 'center',
    borderWidth: 1,
    borderColor: '#e2e8f0',
  },
  modalCancelText: {
    color: '#4a5568',
    fontSize: 16,
    fontWeight: '600',
  },
  modalSaveButton: {
    flex: 1,
    backgroundColor: '#667eea',
    borderRadius: 12,
    padding: 16,
    alignItems: 'center',
  },
  modalSaveText: {
    color: 'white',
    fontSize: 16,
    fontWeight: 'bold',
  },
});

export default CreateMysteryStage;
