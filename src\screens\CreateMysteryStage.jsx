import React, { useState } from 'react';
import {
  View,
  Text,
  TextInput,
  TouchableOpacity,
  StyleSheet,
  ScrollView,
  Alert,
  KeyboardAvoidingView,
  Platform,
} from 'react-native';

const CreateMysteryStage = ({ navigation, route }) => {
  const { stages = [], type } = route.params;
  const [currentStage, setCurrentStage] = useState({
    story: '',
    characters: [{ name: '', statement: '' }],
    clues: [''],
    correctPerson: '',
  });

  const addCharacter = () => {
    setCurrentStage({
      ...currentStage,
      characters: [...currentStage.characters, { name: '', statement: '' }],
    });
  };

  const removeCharacter = (index) => {
    if (currentStage.characters.length > 1) {
      const newCharacters = currentStage.characters.filter((_, i) => i !== index);
      setCurrentStage({ ...currentStage, characters: newCharacters });
    }
  };

  const updateCharacter = (index, field, value) => {
    const newCharacters = [...currentStage.characters];
    newCharacters[index][field] = value;
    setCurrentStage({ ...currentStage, characters: newCharacters });
  };

  const addClue = () => {
    setCurrentStage({
      ...currentStage,
      clues: [...currentStage.clues, ''],
    });
  };

  const removeClue = (index) => {
    if (currentStage.clues.length > 1) {
      const newClues = currentStage.clues.filter((_, i) => i !== index);
      setCurrentStage({ ...currentStage, clues: newClues });
    }
  };

  const updateClue = (index, value) => {
    const newClues = [...currentStage.clues];
    newClues[index] = value;
    setCurrentStage({ ...currentStage, clues: newClues });
  };

  const handleAddStage = () => {
    if (!currentStage.story.trim()) {
      Alert.alert('Eksik Bilgi', 'Lütfen hikaye alanını doldurun.');
      return;
    }

    const validCharacters = currentStage.characters.filter(
      char => char.name.trim() && char.statement.trim()
    );

    if (validCharacters.length < 2) {
      Alert.alert('Eksik Bilgi', 'En az 2 karakter eklemelisiniz.');
      return;
    }

    const validClues = currentStage.clues.filter(clue => clue.trim());

    if (validClues.length === 0) {
      Alert.alert('Eksik Bilgi', 'En az bir ipucu eklemelisiniz.');
      return;
    }

    if (!currentStage.correctPerson.trim()) {
      Alert.alert('Eksik Bilgi', 'Lütfen doğru kişiyi seçin.');
      return;
    }

    const characterNames = validCharacters.map(char => char.name);
    if (!characterNames.includes(currentStage.correctPerson)) {
      Alert.alert('Hata', 'Doğru kişi karakter listesinde bulunmuyor.');
      return;
    }

    const newStages = [...stages, {
      ...currentStage,
      characters: validCharacters,
      clues: validClues,
      type: 'mystery'
    }];
    
    // Yeni boş aşama formu
    setCurrentStage({
      story: '',
      characters: [{ name: '', statement: '' }],
      clues: [''],
      correctPerson: '',
    });

    navigation.setParams({ stages: newStages });
  };

  const handleRemoveStage = (index) => {
    Alert.alert(
      'Aşamayı Sil',
      'Bu aşamayı silmek istediğinizden emin misiniz?',
      [
        { text: 'İptal', style: 'cancel' },
        {
          text: 'Sil',
          style: 'destructive',
          onPress: () => {
            const newStages = stages.filter((_, i) => i !== index);
            navigation.setParams({ stages: newStages });
          },
        },
      ]
    );
  };

  const handleFinish = () => {
    if (stages.length === 0) {
      Alert.alert('Eksik Bilgi', 'En az bir aşama eklemelisiniz.');
      return;
    }

    navigation.navigate('FinalEditor', { stages, type });
  };

  const renderCharacterInput = (character, index) => (
    <View key={index} style={styles.characterContainer}>
      <View style={styles.characterHeader}>
        <Text style={styles.characterTitle}>Karakter {index + 1}</Text>
        {currentStage.characters.length > 1 && (
          <TouchableOpacity
            style={styles.removeButton}
            onPress={() => removeCharacter(index)}
          >
            <Text style={styles.removeButtonText}>🗑️</Text>
          </TouchableOpacity>
        )}
      </View>
      <TextInput
        style={styles.input}
        placeholder="Karakter Adı"
        value={character.name}
        onChangeText={(text) => updateCharacter(index, 'name', text)}
      />
      <TextInput
        style={[styles.textArea, styles.statementInput]}
        placeholder="Karakterin ifadesi/bilgisi"
        value={character.statement}
        onChangeText={(text) => updateCharacter(index, 'statement', text)}
        multiline
        numberOfLines={3}
        textAlignVertical="top"
      />
    </View>
  );

  const renderClueInput = (clue, index) => (
    <View key={index} style={styles.clueContainer}>
      <TextInput
        style={styles.input}
        placeholder={`İpucu ${index + 1}`}
        value={clue}
        onChangeText={(text) => updateClue(index, text)}
      />
      {currentStage.clues.length > 1 && (
        <TouchableOpacity
          style={styles.removeClueButton}
          onPress={() => removeClue(index)}
        >
          <Text style={styles.removeButtonText}>🗑️</Text>
        </TouchableOpacity>
      )}
    </View>
  );

  const renderStagePreview = (stage, index) => (
    <View key={index} style={styles.stagePreview}>
      <View style={styles.stageHeader}>
        <Text style={styles.stageTitle}>Gizem Aşaması {index + 1}</Text>
        <TouchableOpacity
          style={styles.deleteButton}
          onPress={() => handleRemoveStage(index)}
        >
          <Text style={styles.deleteButtonText}>🗑️</Text>
        </TouchableOpacity>
      </View>
      <Text style={styles.stageStory} numberOfLines={3}>
        {stage.story}
      </Text>
      <Text style={styles.stageInfo}>
        {stage.characters.length} karakter, {stage.clues.length} ipucu
      </Text>
      <Text style={styles.stageAnswer}>Doğru kişi: {stage.correctPerson}</Text>
    </View>
  );

  return (
    <KeyboardAvoidingView 
      style={styles.container} 
      behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
    >
      <ScrollView style={styles.scrollContainer} showsVerticalScrollIndicator={false}>
        <View style={styles.header}>
          <Text style={styles.title}>Gizem Aşaması Ekle</Text>
          <Text style={styles.subtitle}>
            Aşama {stages.length + 1} için gizem senaryosu oluşturun
          </Text>
        </View>

        <View style={styles.formContainer}>
          <View style={styles.inputGroup}>
            <Text style={styles.label}>Hikaye *</Text>
            <TextInput
              style={[styles.textArea, styles.storyInput]}
              placeholder="Gizemli hikayeyi buraya yazın..."
              value={currentStage.story}
              onChangeText={(text) => setCurrentStage({ ...currentStage, story: text })}
              multiline
              numberOfLines={5}
              textAlignVertical="top"
            />
          </View>

          <View style={styles.inputGroup}>
            <View style={styles.sectionHeader}>
              <Text style={styles.label}>Karakterler *</Text>
              <TouchableOpacity style={styles.addSmallButton} onPress={addCharacter}>
                <Text style={styles.addSmallButtonText}>+ Ekle</Text>
              </TouchableOpacity>
            </View>
            {currentStage.characters.map(renderCharacterInput)}
          </View>

          <View style={styles.inputGroup}>
            <View style={styles.sectionHeader}>
              <Text style={styles.label}>İpuçları *</Text>
              <TouchableOpacity style={styles.addSmallButton} onPress={addClue}>
                <Text style={styles.addSmallButtonText}>+ Ekle</Text>
              </TouchableOpacity>
            </View>
            {currentStage.clues.map(renderClueInput)}
          </View>

          <View style={styles.inputGroup}>
            <Text style={styles.label}>Doğru Kişi *</Text>
            <TextInput
              style={styles.input}
              placeholder="Doğru kişinin adını girin"
              value={currentStage.correctPerson}
              onChangeText={(text) => setCurrentStage({ ...currentStage, correctPerson: text })}
            />
          </View>

          <TouchableOpacity style={styles.addButton} onPress={handleAddStage}>
            <Text style={styles.addButtonText}>Aşama Ekle</Text>
          </TouchableOpacity>
        </View>

        {stages.length > 0 && (
          <View style={styles.previewContainer}>
            <Text style={styles.previewTitle}>Eklenen Aşamalar ({stages.length})</Text>
            {stages.map(renderStagePreview)}
          </View>
        )}

        <View style={styles.bottomButtons}>
          {stages.length > 0 && (
            <TouchableOpacity style={styles.finishButton} onPress={handleFinish}>
              <Text style={styles.finishButtonText}>Kaydet ve Bitir</Text>
            </TouchableOpacity>
          )}
        </View>
      </ScrollView>
    </KeyboardAvoidingView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f0f4f8',
  },
  scrollContainer: {
    flex: 1,
  },
  header: {
    padding: 20,
    alignItems: 'center',
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#2d3748',
    marginBottom: 8,
  },
  subtitle: {
    fontSize: 16,
    color: '#718096',
    textAlign: 'center',
  },
  formContainer: {
    backgroundColor: 'white',
    margin: 20,
    borderRadius: 16,
    padding: 20,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 4,
  },
  inputGroup: {
    marginBottom: 24,
  },
  sectionHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 12,
  },
  label: {
    fontSize: 16,
    fontWeight: '600',
    color: '#2d3748',
  },
  addSmallButton: {
    backgroundColor: '#e6fffa',
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 8,
  },
  addSmallButtonText: {
    color: '#319795',
    fontSize: 14,
    fontWeight: '600',
  },
  input: {
    borderWidth: 1,
    borderColor: '#e2e8f0',
    borderRadius: 12,
    padding: 16,
    fontSize: 16,
    backgroundColor: '#f7fafc',
    marginBottom: 8,
  },
  textArea: {
    borderWidth: 1,
    borderColor: '#e2e8f0',
    borderRadius: 12,
    padding: 16,
    fontSize: 16,
    backgroundColor: '#f7fafc',
  },
  storyInput: {
    minHeight: 120,
  },
  statementInput: {
    minHeight: 80,
  },
  characterContainer: {
    backgroundColor: '#f7fafc',
    borderRadius: 12,
    padding: 16,
    marginBottom: 12,
  },
  characterHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 12,
  },
  characterTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: '#4299e1',
  },
  removeButton: {
    padding: 4,
  },
  removeButtonText: {
    fontSize: 16,
  },
  clueContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8,
  },
  removeClueButton: {
    marginLeft: 8,
    padding: 8,
  },
  addButton: {
    backgroundColor: '#4299e1',
    borderRadius: 12,
    padding: 16,
    alignItems: 'center',
  },
  addButtonText: {
    color: 'white',
    fontSize: 16,
    fontWeight: '600',
  },
  previewContainer: {
    margin: 20,
    marginTop: 0,
  },
  previewTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#2d3748',
    marginBottom: 16,
  },
  stagePreview: {
    backgroundColor: 'white',
    borderRadius: 12,
    padding: 16,
    marginBottom: 12,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 1,
    },
    shadowOpacity: 0.05,
    shadowRadius: 4,
    elevation: 2,
  },
  stageHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 8,
  },
  stageTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#4299e1',
  },
  deleteButton: {
    padding: 4,
  },
  deleteButtonText: {
    fontSize: 16,
  },
  stageStory: {
    fontSize: 14,
    color: '#2d3748',
    marginBottom: 8,
  },
  stageInfo: {
    fontSize: 14,
    color: '#718096',
    marginBottom: 4,
  },
  stageAnswer: {
    fontSize: 14,
    color: '#48bb78',
    fontWeight: '600',
  },
  bottomButtons: {
    padding: 20,
    paddingTop: 0,
  },
  finishButton: {
    backgroundColor: '#48bb78',
    borderRadius: 12,
    padding: 16,
    alignItems: 'center',
  },
  finishButtonText: {
    color: 'white',
    fontSize: 16,
    fontWeight: '600',
  },
});

export default CreateMysteryStage;
