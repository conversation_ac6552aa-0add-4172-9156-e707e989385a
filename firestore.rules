rules_version = '2';
service cloud.firestore {
  match /databases/{database}/documents {
    // Letters collection - kullanıcılar sadece kendi mektuplarını görebilir ve düzenleyebilir
    match /letters/{document} {
      allow read, write: if request.auth != null && request.auth.uid == resource.data.userId;
      allow create: if request.auth != null && request.auth.uid == request.resource.data.userId;
    }
    
    // Letter attempts collection - kullanıcılar sadece kendi denemelerini görebilir
    match /letterAttempts/{document} {
      allow read, write: if request.auth != null && request.auth.uid == resource.data.userId;
      allow create: if request.auth != null && request.auth.uid == request.resource.data.userId;
    }
  }
}
