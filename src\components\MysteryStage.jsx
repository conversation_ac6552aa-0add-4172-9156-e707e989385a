import React, { useState } from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  StyleSheet,
  ScrollView,
  Modal,
} from 'react-native';
import { Picker } from '@react-native-picker/picker';

const MysteryStage = ({ stage, onAnswer, loading }) => {
  const [selectedPerson, setSelectedPerson] = useState('');
  const [characterModalVisible, setCharacterModalVisible] = useState(false);
  const [clueModalVisible, setClueModalVisible] = useState(false);
  const [selectedCharacter, setSelectedCharacter] = useState(null);

  const handleSubmit = () => {
    if (!selectedPerson) {
      return;
    }
    onAnswer(selectedPerson);
  };

  const renderCharacter = (character, index) => (
    <TouchableOpacity
      key={index}
      style={styles.characterCard}
      onPress={() => {
        setSelectedCharacter(character);
        setCharacterModalVisible(true);
      }}
    >
        <View style={styles.characterIconContainer}>
            <Text style={styles.characterIcon}>👤</Text>
        </View>
        <View style={styles.characterInfo}>
            <Text style={styles.characterName}>{character.name}</Text>
            <Text style={styles.characterHint}>İfadeyi okumak için dokun</Text>
        </View>
    </TouchableOpacity>
  );

  const renderClues = () => (
    <TouchableOpacity
      style={styles.clueCard}
      onPress={() => setClueModalVisible(true)}
    >
        <View style={styles.clueIconContainer}>
            <Text style={styles.clueIcon}>💡</Text>
        </View>
        <View style={styles.clueInfo}>
            <Text style={styles.clueTitle}>Tüm İpuçlarını Oku</Text>
            <Text style={styles.clueHint}>{stage.clues.length} adet ipucu mevcut</Text>
        </View>
    </TouchableOpacity>
  );

  return (
    <View style={styles.container}>
      <View style={styles.card}>
        <Text style={styles.cardTitle}>📜 Gizem</Text>
        <Text style={styles.storyText}>{stage.story}</Text>
      </View>

      <View style={styles.card}>
        <Text style={styles.cardTitle}>👥 Karakterler</Text>
        {stage.characters.map(renderCharacter)}
      </View>

      <View style={styles.card}>
        <Text style={styles.cardTitle}>🔎 İpuçları</Text>
        {renderClues()}
      </View>

      <View style={styles.card}>
        <Text style={styles.cardTitle}>🎯 Suçlu Kim?</Text>
        <View style={styles.pickerContainer}>
          <Picker
            selectedValue={selectedPerson}
            style={styles.picker}
            onValueChange={(itemValue) => setSelectedPerson(itemValue)}
            enabled={!loading}
          >
            <Picker.Item label="Suçluyu seçin..." value="" />
            {stage.characters.map((character, index) => (
              <Picker.Item key={index} label={character.name} value={character.name} />
            ))}
          </Picker>
        </View>

        <TouchableOpacity
          style={[styles.submitButton, (loading || !selectedPerson) && styles.disabledButton]}
          onPress={handleSubmit}
          disabled={loading || !selectedPerson}
        >
          <Text style={styles.submitButtonText}>{loading ? 'Kontrol ediliyor...' : 'Cevabı Gönder'}</Text>
        </TouchableOpacity>
      </View>

      {/* Character Modal */}
      <Modal visible={characterModalVisible} transparent={true} animationType="fade" onRequestClose={() => setCharacterModalVisible(false)}>
        <View style={styles.modalOverlay}>
          <View style={styles.modalContainer}>
            <Text style={styles.modalTitle}>{selectedCharacter?.name}</Text>
            <ScrollView style={styles.modalContent}><Text style={styles.modalText}>{selectedCharacter?.statement}</Text></ScrollView>
            <TouchableOpacity style={styles.modalCloseButton} onPress={() => setCharacterModalVisible(false)}><Text style={styles.modalCloseText}>Kapat</Text></TouchableOpacity>
          </View>
        </View>
      </Modal>

      {/* Clues Modal */}
      <Modal visible={clueModalVisible} transparent={true} animationType="fade" onRequestClose={() => setClueModalVisible(false)}>
        <View style={styles.modalOverlay}>
          <View style={styles.modalContainer}>
            <Text style={styles.modalTitle}>İpuçları</Text>
            <ScrollView style={styles.modalContent}>
              {stage.clues.map((clue, index) => (
                <View key={index} style={styles.clueModalItem}>
                  <Text style={styles.clueModalNumber}>{index + 1}.</Text>
                  <Text style={styles.clueModalText}>{clue}</Text>
                </View>
              ))}
            </ScrollView>
            <TouchableOpacity style={styles.modalCloseButton} onPress={() => setClueModalVisible(false)}><Text style={styles.modalCloseText}>Kapat</Text></TouchableOpacity>
          </View>
        </View>
      </Modal>
    </View>
  );
};

const styles = StyleSheet.create({
  container: { 
      padding: 10, 
      paddingBottom: 50 
    },
  card: {
    backgroundColor: 'white',
    borderRadius: 16,
    padding: 20,
    marginBottom: 16,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.08,
    shadowRadius: 8,
    elevation: 3,
  },
  cardTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#1e293b',
    marginBottom: 16,
  },
  storyText: {
    fontSize: 16,
    color: '#475569',
    lineHeight: 24,
  },
  characterCard: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#f8fafc',
    borderRadius: 12,
    padding: 12,
    marginBottom: 10,
  },
  characterIconContainer: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: '#e0e7ff',
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12,
  },
  characterIcon: { fontSize: 20 },
  characterInfo: { flex: 1 },
  characterName: { fontSize: 16, fontWeight: '600', color: '#312e81' },
  characterHint: { fontSize: 13, color: '#64748b', marginTop: 2 },
  clueCard: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#fefce8',
    borderRadius: 12,
    padding: 12,
  },
  clueIconContainer: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: '#fef08a',
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12,
  },
  clueIcon: { fontSize: 20 },
  clueInfo: { flex: 1 },
  clueTitle: { fontSize: 16, fontWeight: '600', color: '#713f12' },
  clueHint: { fontSize: 13, color: '#a16207', marginTop: 2 },
  pickerContainer: {
    borderWidth: 1,
    borderColor: '#e2e8f0',
    borderRadius: 12,
    backgroundColor: '#f8fafc',
    overflow: 'hidden',
    marginBottom: 16,
  },
  picker: { height: 50, width: '100%' },
  submitButton: {
    backgroundColor: '#4f46e5',
    borderRadius: 12,
    padding: 16,
    alignItems: 'center',
  },
  submitButtonText: { color: 'white', fontSize: 16, fontWeight: 'bold' },
  disabledButton: { opacity: 0.5 },
  modalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.6)',
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
  },
  modalContainer: {
    backgroundColor: 'white',
    borderRadius: 20,
    padding: 24,
    width: '100%',
    maxHeight: '70%',
  },
  modalTitle: {
    fontSize: 22,
    fontWeight: 'bold',
    color: '#1e293b',
    marginBottom: 16,
  },
  modalContent: { maxHeight: '80%' },
  modalText: { fontSize: 16, color: '#475569', lineHeight: 24 },
  modalCloseButton: {
    backgroundColor: '#eef2ff',
    borderRadius: 12,
    padding: 14,
    alignItems: 'center',
    marginTop: 20,
  },
  modalCloseText: { color: '#4338ca', fontSize: 16, fontWeight: '600' },
  clueModalItem: {
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: '#f1f5f9',
    flexDirection: 'row',
  },
  clueModalNumber: {
    fontSize: 16,
    fontWeight: '600',
    color: '#6366f1',
    marginRight: 8,
  },
  clueModalText: { fontSize: 16, color: '#334155', flex: 1 },
});

export default MysteryStage;
