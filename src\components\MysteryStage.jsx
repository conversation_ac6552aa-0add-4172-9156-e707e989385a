import React, { useState } from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  StyleSheet,
  ScrollView,
  Modal,
} from 'react-native';
import { Picker } from '@react-native-picker/picker';

const MysteryStage = ({ stage, onAnswer, loading }) => {
  const [selectedPerson, setSelectedPerson] = useState('');
  const [characterModalVisible, setCharacterModalVisible] = useState(false);
  const [clueModalVisible, setClueModalVisible] = useState(false);
  const [selectedCharacter, setSelectedCharacter] = useState(null);
  const [selectedClues, setSelectedClues] = useState([]);

  const handleSubmit = () => {
    if (!selectedPerson) {
      return;
    }
    onAnswer(selectedPerson);
  };

  const renderCharacter = (character, index) => (
    <TouchableOpacity
      key={index}
      style={styles.characterCard}
      onPress={() => {
        setSelectedCharacter(character);
        setCharacterModalVisible(true);
      }}
    >
      <Text style={styles.characterName}>{character.name}</Text>
      <Text style={styles.characterHint}>İfadeyi okumak için tıklayın</Text>
      <Text style={styles.readIcon}>👁️</Text>
    </TouchableOpacity>
  );

  const renderClue = (clue, index) => (
    <TouchableOpacity
      key={index}
      style={styles.clueItem}
      onPress={() => {
        setSelectedClues(stage.clues);
        setClueModalVisible(true);
      }}
    >
      <Text style={styles.clueNumber}>{index + 1}</Text>
      <Text style={styles.clueText}>İpucu {index + 1} - Okumak için tıklayın</Text>
      <Text style={styles.readIcon}>👁️</Text>
    </TouchableOpacity>
  );



  return (
    <ScrollView style={styles.container} contentContainerStyle={styles.contentContainer} showsVerticalScrollIndicator={false}>
      <View style={styles.storyContainer}>
        <Text style={styles.storyIcon}>🔍</Text>
        <Text style={styles.storyTitle}>Gizem</Text>
        <Text style={styles.storyText}>{stage.story}</Text>
      </View>

      <View style={styles.charactersContainer}>
        <Text style={styles.sectionTitle}>👥 Karakterler ve İfadeleri</Text>
        {stage.characters.map(renderCharacter)}
      </View>

      <View style={styles.cluesContainer}>
        <Text style={styles.sectionTitle}>🔎 İpuçları</Text>
        {stage.clues.map(renderClue)}
      </View>

      <View style={styles.answerContainer}>
        <Text style={styles.answerTitle}>🎯 Suçlu Kim?</Text>
        <Text style={styles.answerSubtitle}>
          Yukarıdaki bilgileri inceleyerek suçluyu seçin:
        </Text>

        <View style={styles.pickerContainer}>
          <Picker
            selectedValue={selectedPerson}
            style={styles.picker}
            onValueChange={(itemValue) => setSelectedPerson(itemValue)}
            enabled={!loading}
          >
            <Picker.Item label="Suçluyu seçin..." value="" />
            {stage.characters.map((character, index) => (
              <Picker.Item
                key={index}
                label={character.name}
                value={character.name}
              />
            ))}
          </Picker>
        </View>

        <TouchableOpacity
          style={[styles.submitButton, loading && styles.disabledButton]}
          onPress={handleSubmit}
          disabled={loading || !selectedPerson}
        >
          <Text style={styles.submitButtonText}>
            {loading ? 'Kontrol ediliyor...' : '🔍 Cevapla'}
          </Text>
        </TouchableOpacity>
      </View>

      <View style={styles.tipContainer}>
        <Text style={styles.tipIcon}>ℹ️</Text>
        <Text style={styles.tipText}>
          Tüm karakterlerin ifadelerini ve ipuçlarını dikkatli bir şekilde okuyun. 
          Yanlış cevap verirseniz mektup 24 saat kilitlenecek.
        </Text>
      </View>

      {/* Character Modal */}
      <Modal
        animationType="slide"
        transparent={true}
        visible={characterModalVisible}
        onRequestClose={() => setCharacterModalVisible(false)}
      >
        <View style={styles.modalOverlay}>
          <View style={styles.modalContainer}>
            <View style={styles.modalHeader}>
              <Text style={styles.modalTitle}>👤 {selectedCharacter?.name}</Text>
              <TouchableOpacity onPress={() => setCharacterModalVisible(false)}>
                <Text style={styles.closeButton}>✕</Text>
              </TouchableOpacity>
            </View>
            <ScrollView style={styles.modalContent} showsVerticalScrollIndicator={false}>
              <Text style={styles.modalText}>{selectedCharacter?.statement}</Text>
            </ScrollView>
            <TouchableOpacity
              style={styles.modalCloseButton}
              onPress={() => setCharacterModalVisible(false)}
            >
              <Text style={styles.modalCloseText}>Kapat</Text>
            </TouchableOpacity>
          </View>
        </View>
      </Modal>

      {/* Clues Modal */}
      <Modal
        animationType="slide"
        transparent={true}
        visible={clueModalVisible}
        onRequestClose={() => setClueModalVisible(false)}
      >
        <View style={styles.modalOverlay}>
          <View style={styles.modalContainer}>
            <View style={styles.modalHeader}>
              <Text style={styles.modalTitle}>💡 İpuçları</Text>
              <TouchableOpacity onPress={() => setClueModalVisible(false)}>
                <Text style={styles.closeButton}>✕</Text>
              </TouchableOpacity>
            </View>
            <ScrollView style={styles.modalContent} showsVerticalScrollIndicator={false}>
              {selectedClues.map((clue, index) => (
                <View key={index} style={styles.clueModalItem}>
                  <Text style={styles.clueModalNumber}>İpucu {index + 1}</Text>
                  <Text style={styles.clueModalText}>{clue}</Text>
                </View>
              ))}
            </ScrollView>
            <TouchableOpacity
              style={styles.modalCloseButton}
              onPress={() => setClueModalVisible(false)}
            >
              <Text style={styles.modalCloseText}>Kapat</Text>
            </TouchableOpacity>
          </View>
        </View>
      </Modal>
    </ScrollView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f7fafc',
  },
  contentContainer: {
    padding: 20,
  },
  storyContainer: {
    backgroundColor: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
    borderRadius: 24,
    padding: 28,
    marginBottom: 24,
    alignItems: 'center',
    shadowColor: '#667eea',
    shadowOffset: {
      width: 0,
      height: 8,
    },
    shadowOpacity: 0.3,
    shadowRadius: 16,
    elevation: 8,
    borderWidth: 2,
    borderColor: '#a78bfa',
  },
  storyIcon: {
    fontSize: 56,
    marginBottom: 16,
    textShadowColor: 'rgba(255, 255, 255, 0.3)',
    textShadowOffset: { width: 2, height: 2 },
    textShadowRadius: 4,
  },
  storyTitle: {
    fontSize: 24,
    fontWeight: 'bold',
    color: 'white',
    marginBottom: 20,
    textShadowColor: 'rgba(0, 0, 0, 0.3)',
    textShadowOffset: { width: 1, height: 1 },
    textShadowRadius: 2,
  },
  storyText: {
    fontSize: 17,
    color: 'white',
    textAlign: 'center',
    lineHeight: 26,
    fontWeight: '500',
    textShadowColor: 'rgba(0, 0, 0, 0.2)',
    textShadowOffset: { width: 1, height: 1 },
    textShadowRadius: 2,
  },
  charactersContainer: {
    backgroundColor: 'white',
    borderRadius: 20,
    padding: 24,
    marginBottom: 24,
    shadowColor: '#f97316',
    shadowOffset: {
      width: 0,
      height: 6,
    },
    shadowOpacity: 0.15,
    shadowRadius: 12,
    elevation: 6,
    borderWidth: 2,
    borderColor: '#fed7aa',
  },
  sectionTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#1a202c',
    marginBottom: 20,
    textAlign: 'center',
  },
  characterCard: {
    backgroundColor: '#ffecd2',
    borderRadius: 16,
    padding: 20,
    marginBottom: 16,
    borderLeftWidth: 6,
    borderLeftColor: '#f97316',
    shadowColor: '#f97316',
    shadowOffset: {
      width: 0,
      height: 4,
    },
    shadowOpacity: 0.2,
    shadowRadius: 8,
    elevation: 4,
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  characterName: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#c2410c',
    flex: 1,
  },
  characterHint: {
    fontSize: 12,
    color: '#6b7280',
    fontStyle: 'italic',
    flex: 1,
  },
  readIcon: {
    fontSize: 20,
    marginLeft: 8,
  },
  cluesContainer: {
    backgroundColor: 'white',
    borderRadius: 20,
    padding: 24,
    marginBottom: 24,
    shadowColor: '#10b981',
    shadowOffset: {
      width: 0,
      height: 6,
    },
    shadowOpacity: 0.15,
    shadowRadius: 12,
    elevation: 6,
    borderWidth: 2,
    borderColor: '#a7f3d0',
  },
  clueItem: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 16,
    backgroundColor: '#a8edea',
    borderRadius: 16,
    padding: 16,
    justifyContent: 'space-between',
  },
  clueNumber: {
    width: 32,
    height: 32,
    borderRadius: 16,
    backgroundColor: '#10b981',
    color: 'white',
    textAlign: 'center',
    fontSize: 14,
    fontWeight: 'bold',
    lineHeight: 32,
    marginRight: 16,
    shadowColor: '#10b981',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.3,
    shadowRadius: 4,
    elevation: 2,
  },
  clueText: {
    flex: 1,
    fontSize: 15,
    color: '#1a202c',
    fontWeight: '500',
  },
  answerContainer: {
    backgroundColor: 'white',
    borderRadius: 20,
    padding: 24,
    marginBottom: 24,
    shadowColor: '#dc2626',
    shadowOffset: {
      width: 0,
      height: 6,
    },
    shadowOpacity: 0.15,
    shadowRadius: 12,
    elevation: 6,
    borderWidth: 2,
    borderColor: '#fecaca',
  },
  answerTitle: {
    fontSize: 22,
    fontWeight: 'bold',
    color: '#dc2626',
    marginBottom: 12,
    textAlign: 'center',
    textShadowColor: 'rgba(220, 38, 38, 0.2)',
    textShadowOffset: { width: 1, height: 1 },
    textShadowRadius: 2,
  },
  answerSubtitle: {
    fontSize: 16,
    color: '#4a5568',
    textAlign: 'center',
    marginBottom: 24,
    fontWeight: '500',
  },
  pickerContainer: {
    borderWidth: 2,
    borderColor: '#667eea',
    borderRadius: 16,
    backgroundColor: 'linear-gradient(135deg, #ebf4ff 0%, #dbeafe 100%)',
    marginBottom: 24,
    shadowColor: '#667eea',
    shadowOffset: {
      width: 0,
      height: 4,
    },
    shadowOpacity: 0.2,
    shadowRadius: 8,
    elevation: 4,
    overflow: 'hidden',
  },
  picker: {
    height: 60,
    width: '100%',
    color: '#1a202c',
    fontSize: 17,
    fontWeight: '600',
  },
  submitButton: {
    backgroundColor: 'linear-gradient(135deg, #dc2626 0%, #b91c1c 100%)',
    borderRadius: 20,
    padding: 20,
    alignItems: 'center',
    shadowColor: '#dc2626',
    shadowOffset: {
      width: 0,
      height: 6,
    },
    shadowOpacity: 0.3,
    shadowRadius: 12,
    elevation: 6,
  },
  submitButtonText: {
    color: 'white',
    fontSize: 18,
    fontWeight: 'bold',
    textShadowColor: 'rgba(0, 0, 0, 0.3)',
    textShadowOffset: { width: 1, height: 1 },
    textShadowRadius: 2,
  },
  disabledButton: {
    opacity: 0.6,
  },
  tipContainer: {
    flexDirection: 'row',
    backgroundColor: 'linear-gradient(135deg, #e6fffa 0%, #f0fff4 100%)',
    borderRadius: 16,
    padding: 20,
    alignItems: 'flex-start',
    borderWidth: 2,
    borderColor: '#a7f3d0',
    shadowColor: '#10b981',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
  },
  tipIcon: {
    fontSize: 24,
    marginRight: 16,
  },
  tipText: {
    flex: 1,
    fontSize: 15,
    color: '#1a202c',
    lineHeight: 22,
    fontWeight: '500',
  },
  // Modal styles
  modalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
  },
  modalContainer: {
    backgroundColor: 'white',
    borderRadius: 20,
    padding: 24,
    width: '100%',
    maxHeight: '70%',
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 10,
    },
    shadowOpacity: 0.25,
    shadowRadius: 20,
    elevation: 10,
  },
  modalHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 20,
    paddingBottom: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#e2e8f0',
  },
  modalTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#1a202c',
  },
  closeButton: {
    fontSize: 24,
    color: '#718096',
    fontWeight: 'bold',
  },
  modalContent: {
    flex: 1,
    marginVertical: 8,
  },
  modalText: {
    fontSize: 16,
    color: '#4a5568',
    lineHeight: 24,
  },
  modalCloseButton: {
    backgroundColor: '#667eea',
    borderRadius: 12,
    padding: 16,
    alignItems: 'center',
    marginTop: 16,
  },
  modalCloseText: {
    color: 'white',
    fontSize: 16,
    fontWeight: 'bold',
  },
  clueModalItem: {
    backgroundColor: '#f0f9ff',
    borderRadius: 12,
    padding: 16,
    marginBottom: 12,
    borderLeftWidth: 4,
    borderLeftColor: '#0ea5e9',
  },
  clueModalNumber: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#0ea5e9',
    marginBottom: 8,
  },
  clueModalText: {
    fontSize: 15,
    color: '#374151',
    lineHeight: 22,
  },
});

export default MysteryStage;
