import React, { useState } from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  StyleSheet,
  ScrollView,
} from 'react-native';

const MysteryStage = ({ stage, onAnswer, loading }) => {
  const [selectedPerson, setSelectedPerson] = useState('');

  const handleSubmit = () => {
    if (!selectedPerson) {
      return;
    }
    onAnswer(selectedPerson);
  };

  const renderCharacter = (character, index) => (
    <View key={index} style={styles.characterCard}>
      <Text style={styles.characterName}>{character.name}</Text>
      <Text style={styles.characterStatement}>{character.statement}</Text>
    </View>
  );

  const renderClue = (clue, index) => (
    <View key={index} style={styles.clueItem}>
      <Text style={styles.clueNumber}>{index + 1}</Text>
      <Text style={styles.clueText}>{clue}</Text>
    </View>
  );

  const renderPersonOption = (character, index) => (
    <TouchableOpacity
      key={index}
      style={[
        styles.personOption,
        selectedPerson === character.name && styles.selectedPersonOption
      ]}
      onPress={() => setSelectedPerson(character.name)}
      disabled={loading}
    >
      <View style={[
        styles.radioButton,
        selectedPerson === character.name && styles.selectedRadioButton
      ]}>
        {selectedPerson === character.name && (
          <View style={styles.radioButtonInner} />
        )}
      </View>
      <Text style={[
        styles.personOptionText,
        selectedPerson === character.name && styles.selectedPersonOptionText
      ]}>
        {character.name}
      </Text>
    </TouchableOpacity>
  );

  return (
    <ScrollView style={styles.container} contentContainerStyle={styles.contentContainer}>
      <View style={styles.storyContainer}>
        <Text style={styles.storyIcon}>🔍</Text>
        <Text style={styles.storyTitle}>Gizem</Text>
        <Text style={styles.storyText}>{stage.story}</Text>
      </View>

      <View style={styles.charactersContainer}>
        <Text style={styles.sectionTitle}>👥 Karakterler ve İfadeleri</Text>
        {stage.characters.map(renderCharacter)}
      </View>

      <View style={styles.cluesContainer}>
        <Text style={styles.sectionTitle}>🔎 İpuçları</Text>
        {stage.clues.map(renderClue)}
      </View>

      <View style={styles.answerContainer}>
        <Text style={styles.answerTitle}>🎯 Suçlu Kim?</Text>
        <Text style={styles.answerSubtitle}>
          Yukarıdaki bilgileri inceleyerek suçluyu seçin:
        </Text>
        
        <View style={styles.personsContainer}>
          {stage.characters.map(renderPersonOption)}
        </View>

        <TouchableOpacity
          style={[styles.submitButton, loading && styles.disabledButton]}
          onPress={handleSubmit}
          disabled={loading || !selectedPerson}
        >
          <Text style={styles.submitButtonText}>
            {loading ? 'Kontrol ediliyor...' : '🔍 Cevapla'}
          </Text>
        </TouchableOpacity>
      </View>

      <View style={styles.tipContainer}>
        <Text style={styles.tipIcon}>ℹ️</Text>
        <Text style={styles.tipText}>
          Tüm karakterlerin ifadelerini ve ipuçlarını dikkatli bir şekilde okuyun. 
          Yanlış cevap verirseniz mektup 24 saat kilitlenecek.
        </Text>
      </View>
    </ScrollView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f0f4f8',
  },
  contentContainer: {
    padding: 20,
  },
  storyContainer: {
    backgroundColor: 'white',
    borderRadius: 16,
    padding: 24,
    marginBottom: 20,
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 4,
  },
  storyIcon: {
    fontSize: 48,
    marginBottom: 12,
  },
  storyTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#2d3748',
    marginBottom: 16,
  },
  storyText: {
    fontSize: 16,
    color: '#4a5568',
    textAlign: 'center',
    lineHeight: 24,
  },
  charactersContainer: {
    backgroundColor: 'white',
    borderRadius: 16,
    padding: 20,
    marginBottom: 20,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 4,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#2d3748',
    marginBottom: 16,
  },
  characterCard: {
    backgroundColor: '#f7fafc',
    borderRadius: 12,
    padding: 16,
    marginBottom: 12,
    borderLeftWidth: 4,
    borderLeftColor: '#4299e1',
  },
  characterName: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#2d3748',
    marginBottom: 8,
  },
  characterStatement: {
    fontSize: 14,
    color: '#4a5568',
    lineHeight: 20,
  },
  cluesContainer: {
    backgroundColor: 'white',
    borderRadius: 16,
    padding: 20,
    marginBottom: 20,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 4,
  },
  clueItem: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    marginBottom: 12,
  },
  clueNumber: {
    width: 24,
    height: 24,
    borderRadius: 12,
    backgroundColor: '#fef5e7',
    color: '#744210',
    textAlign: 'center',
    fontSize: 12,
    fontWeight: 'bold',
    lineHeight: 24,
    marginRight: 12,
  },
  clueText: {
    flex: 1,
    fontSize: 14,
    color: '#4a5568',
    lineHeight: 20,
  },
  answerContainer: {
    backgroundColor: 'white',
    borderRadius: 16,
    padding: 20,
    marginBottom: 20,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 4,
  },
  answerTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#2d3748',
    marginBottom: 8,
    textAlign: 'center',
  },
  answerSubtitle: {
    fontSize: 14,
    color: '#718096',
    textAlign: 'center',
    marginBottom: 20,
  },
  personsContainer: {
    marginBottom: 20,
  },
  personOption: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 16,
    borderRadius: 12,
    borderWidth: 1,
    borderColor: '#e2e8f0',
    marginBottom: 8,
    backgroundColor: '#f7fafc',
  },
  selectedPersonOption: {
    borderColor: '#4299e1',
    backgroundColor: '#ebf8ff',
  },
  radioButton: {
    width: 20,
    height: 20,
    borderRadius: 10,
    borderWidth: 2,
    borderColor: '#e2e8f0',
    marginRight: 12,
    justifyContent: 'center',
    alignItems: 'center',
  },
  selectedRadioButton: {
    borderColor: '#4299e1',
  },
  radioButtonInner: {
    width: 10,
    height: 10,
    borderRadius: 5,
    backgroundColor: '#4299e1',
  },
  personOptionText: {
    fontSize: 16,
    color: '#4a5568',
  },
  selectedPersonOptionText: {
    color: '#2d3748',
    fontWeight: '600',
  },
  submitButton: {
    backgroundColor: '#4299e1',
    borderRadius: 12,
    padding: 16,
    alignItems: 'center',
  },
  submitButtonText: {
    color: 'white',
    fontSize: 16,
    fontWeight: '600',
  },
  disabledButton: {
    opacity: 0.6,
  },
  tipContainer: {
    flexDirection: 'row',
    backgroundColor: '#e6fffa',
    borderRadius: 12,
    padding: 16,
    alignItems: 'flex-start',
  },
  tipIcon: {
    fontSize: 20,
    marginRight: 12,
  },
  tipText: {
    flex: 1,
    fontSize: 14,
    color: '#2d3748',
    lineHeight: 20,
  },
});

export default MysteryStage;
