# 🚀 Mystery Letter - <PERSON><PERSON><PERSON>hber, Mystery Letter uygulamasını çalıştırmak için gerekli adımları açıklar.

## 📋 Gereksinimler

- Node.js (v14 veya üzeri)
- npm veya yarn
- Expo CLI
- Firebase hesabı

## 🔧 Firebase Kurulumu

### 1. Firebase Projesi Oluşturma

1. [Firebase Console](https://console.firebase.google.com/)'a gidin
2. "Create a project" butonuna tıklayın
3. <PERSON>je adını girin (örn: "mystery-letter")
4. Google Analytics'i etkinleştirin (isteğe bağlı)
5. Projeyi oluşturun

### 2. Firestore Database Kurulumu

1. Sol menüden "Firestore Database" seçin
2. "Create database" butonuna tıklayın
3. "Start in test mode" seçin (geliştirme için)
4. Lokasyon seçin (Europe-west3 önerilir)
5. "Done" butonuna tıklayın

### 3. Firestore Güvenlik Kuralları

1. Firestore Database > Rules sekmesine gidin
2. Aşağıdaki kuralları yapıştırın:

```javascript
rules_version = '2';
service cloud.firestore {
  match /databases/{database}/documents {
    // Letters collection - herkes okuyabilir ve yazabilir
    match /letters/{document} {
      allow read, write: if true;
    }

    // Letter attempts collection - herkes okuyabilir ve yazabilir
    match /letterAttempts/{document} {
      allow read, write: if true;
    }
  }
}
```

3. "Publish" butonuna tıklayın

### 4. Web App Yapılandırması

1. Firebase Console'da proje ayarlarına gidin (⚙️ ikonu)
2. "General" sekmesinde "Your apps" bölümüne gidin
3. Web app ikonu (</>) tıklayın
4. App nickname girin (örn: "mystery-letter-web")
5. "Register app" butonuna tıklayın
6. Firebase config bilgilerini kopyalayın

## 🛠️ Uygulama Kurulumu

### 1. Projeyi İndirin

```bash
# Eğer Git repository'si varsa
git clone <repository-url>
cd MysteryLetter

# Veya dosyaları manuel olarak indirin
```

### 2. Bağımlılıkları Yükleyin

```bash
npm install
```

### 3. Firebase Yapılandırması

1. `src/config/firebase.js` dosyasını açın
2. Firebase Console'dan aldığınız config bilgilerini yapıştırın:

```javascript
const firebaseConfig = {
  apiKey: "your-api-key",
  authDomain: "your-project.firebaseapp.com",
  projectId: "your-project-id",
  storageBucket: "your-project.appspot.com",
  messagingSenderId: "123456789",
  appId: "your-app-id",
  measurementId: "G-XXXXXXXXXX"
};
```

### 4. Uygulamayı Başlatın

```bash
npm start
```

## 📱 Test Etme

### Expo Go ile Test (Mobil)

1. Telefonunuza Expo Go uygulamasını indirin
2. Terminal'de görünen QR kodu tarayın
3. Uygulama telefonunuzda açılacak

### Web Tarayıcısında Test

1. Terminal'de `w` tuşuna basın
2. Uygulama tarayıcıda açılacak

### Android Emulator

1. Android Studio'da emulator başlatın
2. Terminal'de `a` tuşuna basın

## 🔍 Sorun Giderme

### Firebase Bağlantı Hatası

- Firebase config bilgilerini kontrol edin
- Internet bağlantınızı kontrol edin
- Firebase Console'da projenin aktif olduğunu kontrol edin



### Firestore Hatası

- Firestore kurallarının doğru ayarlandığını kontrol edin
- Firebase Console > Firestore Database'in oluşturulduğunu kontrol edin

### Metro Bundler Hatası

```bash
# Cache'i temizleyin
npx expo start --clear

# Node modules'i yeniden yükleyin
rm -rf node_modules
npm install
```

## 📊 Veri Yapısı

### Letters Collection

```javascript
{
  id: "document-id",
  userId: "user-uid",
  title: "Mektup Başlığı",
  type: "question" | "mystery",
  stages: [
    {
      type: "question",
      question: "Soru metni",
      answer: "Doğru cevap",
      hint: "İpucu (opsiyonel)"
    }
    // veya
    {
      type: "mystery",
      story: "Hikaye metni",
      characters: [
        {
          name: "Karakter adı",
          statement: "Karakter ifadesi"
        }
      ],
      clues: ["İpucu 1", "İpucu 2"],
      correctPerson: "Doğru kişi"
    }
  ],
  finalLetter: {
    title: "Son mektup başlığı",
    content: "Son mektup içeriği"
  },
  currentStage: 0,
  isCompleted: false,
  createdAt: timestamp,
  updatedAt: timestamp
}
```

### LetterAttempts Collection

```javascript
{
  id: "document-id",
  userId: "user-uid",
  letterId: "letter-document-id",
  lockedUntil: timestamp | null,
  wrongAnswers: number,
  createdAt: timestamp,
  updatedAt: timestamp
}
```

## 🎯 Özellikler

- ✅ Authentication olmadan kullanım
- ✅ Şifreli soru mektupları
- ✅ Dedektiflik gizemi mektupları
- ✅ Çoklu aşama desteği
- ✅ Yanlış cevap kilitleme sistemi
- ✅ Gerçek zamanlı veri senkronizasyonu
- ✅ Responsive tasarım
- ✅ Modern UI/UX

## 📞 Destek

Herhangi bir sorun yaşarsanız:

1. Bu rehberi tekrar kontrol edin
2. Firebase Console'da logları inceleyin
3. Browser developer tools'da console'u kontrol edin
4. GitHub Issues'da sorun bildirin

---

🎉 **Kurulum tamamlandı! Mystery Letter uygulamanızı kullanmaya başlayabilirsiniz.**
