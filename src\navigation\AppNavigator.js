import React from 'react';
import { NavigationContainer } from '@react-navigation/native';
import { createStackNavigator } from '@react-navigation/stack';

// Import screens
import HomePage from '../screens/HomePage';
import CreatorPage from '../screens/CreatorPage';
import CreateQuestionStage from '../screens/CreateQuestionStage';
import CreateMysteryStage from '../screens/CreateMysteryStage';
import FinalLetterEditor from '../screens/FinalLetterEditor';
import LetterPlayPage from '../screens/LetterPlayPage';
import FinalLetter from '../screens/FinalLetter';

const Stack = createStackNavigator();

const AppNavigator = () => {
  return (
    <NavigationContainer>
      <Stack.Navigator
        initialRouteName="Home"
        screenOptions={{
          headerStyle: {
            backgroundColor: '#f8f9fa',
          },
          headerTintColor: '#333',
          headerTitleStyle: {
            fontWeight: 'bold',
          },
        }}
      >
        <Stack.Screen
          name="Home"
          component={HomePage}
          options={{ title: 'Mektuplarım' }}
        />
        <Stack.Screen
          name="Creator"
          component={CreatorPage}
          options={{ title: 'Mektup Türü Seç' }}
        />
        <Stack.Screen
          name="CreateQuestion"
          component={CreateQuestionStage}
          options={{ title: 'Soru Aşaması Ekle' }}
        />
        <Stack.Screen
          name="CreateMystery"
          component={CreateMysteryStage}
          options={{ title: 'Gizem Aşaması Ekle' }}
        />
        <Stack.Screen
          name="FinalEditor"
          component={FinalLetterEditor}
          options={{ title: 'Son Mektubu Yaz' }}
        />
        <Stack.Screen
          name="LetterPlay"
          component={LetterPlayPage}
          options={{ title: 'Mektup Çözme' }}
        />
        <Stack.Screen
          name="FinalLetter"
          component={FinalLetter}
          options={{ headerShown: false }}
        />
      </Stack.Navigator>
    </NavigationContainer>
  );
};

export default AppNavigator;
