import React from 'react';
import { NavigationContainer } from '@react-navigation/native';
import { createStackNavigator } from '@react-navigation/stack';

// Import screens
import LoginPage from '../screens/LoginPage';
import HomePage from '../screens/HomePage';
import CreatorPage from '../screens/CreatorPage';
import CreateQuestionStage from '../screens/CreateQuestionStage';
import CreateMysteryStage from '../screens/CreateMysteryStage';
import FinalLetterEditor from '../screens/FinalLetterEditor';
import LetterPlayPage from '../screens/LetterPlayPage';
import FinalLetter from '../screens/FinalLetter';

const Stack = createStackNavigator();

const AppNavigator = () => {
  return (
    <NavigationContainer>
      <Stack.Navigator 
        initialRouteName="Login"
        screenOptions={{
          headerStyle: {
            backgroundColor: '#f8f9fa',
          },
          headerTintColor: '#333',
          headerTitleStyle: {
            fontWeight: 'bold',
          },
        }}
      >
        <Stack.Screen 
          name="Login" 
          component={LoginPage} 
          options={{ headerShown: false }}
        />
        <Stack.Screen 
          name="Home" 
          component={HomePage} 
          options={{  headerShown: false  }}
        />
        <Stack.Screen 
          name="Creator" 
          component={CreatorPage} 
          options={{  headerShown: false  }}
        />
        <Stack.Screen 
          name="CreateQuestion" 
          component={CreateQuestionStage} 
          options={{  headerShown: false  }}
        />
        <Stack.Screen 
          name="CreateMystery" 
          component={CreateMysteryStage} 
          options={{  headerShown: false  }}
        />
        <Stack.Screen 
          name="FinalEditor" 
          component={FinalLetterEditor} 
          options={{  headerShown: false  }}
        />
        <Stack.Screen 
          name="LetterPlay" 
          component={LetterPlayPage} 
          options={{  headerShown: false  }}
        />
        <Stack.Screen 
          name="FinalLetter" 
          component={FinalLetter} 
          options={{ headerShown: false }}
        />
      </Stack.Navigator>
    </NavigationContainer>
  );
};

export default AppNavigator;
