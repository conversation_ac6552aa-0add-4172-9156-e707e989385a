import React from 'react';
import { NavigationContainer } from '@react-navigation/native';
import { createStackNavigator } from '@react-navigation/stack';

// Import screens
import HomePage from '../screens/HomePage';
import CreatorPage from '../screens/CreatorPage';
import CreateQuestionStage from '../screens/CreateQuestionStage';
import CreateMysteryStage from '../screens/CreateMysteryStage';
import FinalLetterEditor from '../screens/FinalLetterEditor';
import LetterPlayPage from '../screens/LetterPlayPage';
import FinalLetter from '../screens/FinalLetter';

const Stack = createStackNavigator();

const AppNavigator = () => {
  return (
    <NavigationContainer>
      <Stack.Navigator
        initialRouteName="Home"
        screenOptions={{
          headerShown: false, // Tüm header'ları gizle, kendi custom header'larımızı kullanacağız
        }}
      >
        <Stack.Screen
          name="Home"
          component={HomePage}
        />
        <Stack.Screen
          name="Creator"
          component={CreatorPage}
        />
        <Stack.Screen
          name="CreateQuestion"
          component={CreateQuestionStage}
        />
        <Stack.Screen
          name="CreateMystery"
          component={CreateMysteryStage}
        />
        <Stack.Screen
          name="FinalEditor"
          component={FinalLetterEditor}
        />
        <Stack.Screen
          name="LetterPlay"
          component={LetterPlayPage}
        />
        <Stack.Screen
          name="FinalLetter"
          component={FinalLetter}
        />
      </Stack.Navigator>
    </NavigationContainer>
  );
};

export default AppNavigator;
