import React, { useEffect, useState } from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  StyleSheet,
  ScrollView,
  Animated,
  Dimensions,
  Share,
  Alert,
  Platform,
} from 'react-native';
import * as Print from 'expo-print';
import * as Sharing from 'expo-sharing';

const { width } = Dimensions.get('window');

const FinalLetter = ({ navigation, route }) => {
  const { letter } = route.params;
  const [fadeAnim] = useState(new Animated.Value(0));
  const [scaleAnim] = useState(new Animated.Value(0.8));
  const [heartAnim] = useState(new Animated.Value(0));

  useEffect(() => {
    Animated.sequence([
      Animated.parallel([
        Animated.timing(fadeAnim, { toValue: 1, duration: 1000, useNativeDriver: true }),
        Animated.spring(scaleAnim, { toValue: 1, tension: 50, friction: 7, useNativeDriver: true }),
      ]),
      Animated.loop(
        Animated.sequence([
          Animated.timing(heartAnim, { toValue: 1, duration: 1000, useNativeDriver: true }),
          Animated.timing(heartAnim, { toValue: 0, duration: 1000, useNativeDriver: true }),
        ])
      ),
    ]).start();
  }, []);

  const heartScale = heartAnim.interpolate({ inputRange: [0, 1], outputRange: [1, 1.2] });
  const heartOpacity = heartAnim.interpolate({ inputRange: [0, 0.5, 1], outputRange: [0.7, 1, 0.7] });

  const createPDFTemplate = () => {
    const currentDate = new Date().toLocaleDateString('tr-TR', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });

    return `
    <!DOCTYPE html>
    <html>
    <head>
        <meta charset="UTF-8">
        <title>Mystery Letter - ${letter.finalLetter?.title || letter.title}</title>
        <style>
            body {
                font-family: 'Georgia', serif;
                line-height: 1.6;
                color: #2d3748;
                max-width: 800px;
                margin: 0 auto;
                padding: 40px;
                background: linear-gradient(135deg, #fef5e7 0%, #fff8e1 100%);
            }
            .letter-container {
                background: white;
                border-radius: 20px;
                padding: 40px;
                box-shadow: 0 20px 40px rgba(0,0,0,0.1);
                border: 3px solid #f6e05e;
            }
            .header {
                text-align: center;
                margin-bottom: 40px;
                border-bottom: 3px solid #f6e05e;
                padding-bottom: 20px;
            }
            .title {
                font-size: 32px;
                font-weight: bold;
                color: #c2410c;
                margin-bottom: 10px;
                text-shadow: 2px 2px 4px rgba(0,0,0,0.1);
            }
            .subtitle {
                font-size: 18px;
                color: #718096;
                font-style: italic;
            }
            .content {
                font-size: 16px;
                line-height: 1.8;
                margin-bottom: 40px;
                text-align: justify;
                background: #f8fafc;
                padding: 30px;
                border-radius: 15px;
                border-left: 6px solid #f6e05e;
            }
            .footer {
                text-align: center;
                margin-top: 40px;
                padding-top: 20px;
                border-top: 2px solid #e2e8f0;
                color: #718096;
                font-size: 14px;
            }
            .signature {
                text-align: right;
                margin-top: 30px;
                font-style: italic;
                color: #4a5568;
            }
            .decoration {
                text-align: center;
                font-size: 24px;
                margin: 20px 0;
            }
        </style>
    </head>
    <body>
        <div class="letter-container">
            <div class="header">
                <div class="title">💌 ${letter.finalLetter?.title || letter.title}</div>
                <div class="subtitle">Özel Mektup Sertifikası</div>
            </div>

            <div class="decoration">✨ 🎉 ✨</div>

            <div class="content">
                ${letter.finalLetter?.content || 'Bu özel mektup sizin için hazırlandı. Tüm aşamaları başarıyla çözerek buraya ulaştınız!'}
            </div>

            <div class="signature">
                Bu mektup Mystery Letter uygulaması ile oluşturulmuştur.
            </div>

            <div class="footer">
                <div class="decoration">🌟 Tebrikler! 🌟</div>
                <p>Bu özel anınızı hatırlamak için saklayın.</p>
            </div>
        </div>
    </body>
    </html>
    `;
  };

  const handleSharePDF = async () => {
    try {
      const htmlContent = createPDFTemplate();

      const { uri } = await Print.printToFileAsync({
        html: htmlContent,
        base64: false,
      });

      if (await Sharing.isAvailableAsync()) {
        await Sharing.shareAsync(uri, {
          mimeType: 'application/pdf',
          dialogTitle: 'Mystery Letter PDF',
          UTI: 'com.adobe.pdf',
        });
      } else {
        Alert.alert('Hata', 'PDF paylaşımı desteklenmiyor.');
      }
    } catch (error) {
      console.error('PDF oluşturma veya paylaşma hatası:', error);
      Alert.alert('Hata', 'PDF oluşturulurken veya paylaşılırken bir sorun oluştu.');
    }
  };

  return (
    <View style={styles.container}>
      <ScrollView 
        contentContainerStyle={styles.scrollContainer}
        showsVerticalScrollIndicator={false}
      >
        <Animated.View 
          style={[
            styles.congratsContainer,
            {
              opacity: fadeAnim,
              transform: [{ scale: scaleAnim }],
            },
          ]}
        >
          <Animated.Text 
            style={[
              styles.congratsIcon,
              {
                transform: [{ scale: heartScale }],
                opacity: heartOpacity,
              },
            ]}
          >
            🎉
          </Animated.Text>
          <Text style={styles.congratsTitle}>Tebrikler!</Text>
          <Text style={styles.congratsSubtitle}>
            Mektubu Başarıyla Çözdün!
          </Text>
        </Animated.View>

        <Animated.View 
          style={[
            styles.letterContainer,
            {
              opacity: fadeAnim,
              transform: [{ scale: scaleAnim }],
            },
          ]}
        >
          <View style={styles.letterHeader}>
            <Text style={styles.letterTitle}>
              {letter.finalLetter?.title || letter.title}
            </Text>
            <View style={styles.decorativeLine} />
          </View>

          <View style={styles.letterBody}>
            <Text style={styles.letterContent}>
              {letter.finalLetter?.content || 'Bu özel mektup sizin için hazırlandı.'}
            </Text>
          </View>

          <View style={styles.letterFooter}>
            <Text style={styles.completionDate}>
              {new Date().toLocaleDateString('tr-TR', {
                day: 'numeric',
                month: 'long',
                year: 'numeric',
              })}
            </Text>
            <View style={styles.heartContainer}>
              <Animated.Text 
                style={[
                  styles.heartIcon,
                  {
                    transform: [{ scale: heartScale }],
                    opacity: heartOpacity,
                  },
                ]}
              >
                💖
              </Animated.Text>
            </View>
          </View>
        </Animated.View>

        <View style={styles.messageContainer}>
          <Text style={styles.messageIcon}>💌</Text>
          <Text style={styles.messageText}>
            Bu özel mektup sadece sizin için hazırlandı.
            Tüm aşamaları başarıyla çözerek buraya ulaştınız!
          </Text>
          <TouchableOpacity style={styles.shareButton} onPress={handleSharePDF}>
            <Text style={styles.shareButtonText}>📄 PDF Olarak Paylaş</Text>
          </TouchableOpacity>
        </View>
      </ScrollView>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#c0a4a4ff',
  },
  scrollContainer: {
    padding: 20,
    paddingBottom: 100,
  },
  congratsContainer: {
    alignItems: 'center',
    marginBottom: 30,
  },
  congratsIcon: {
    fontSize: 64,
    marginBottom: 16,
  },
  congratsTitle: {
    fontSize: 32,
    fontWeight: 'bold',
    color: '#2d3748',
    marginBottom: 8,
    textAlign: 'center',
  },
  congratsSubtitle: {
    fontSize: 18,
    color: '#4a5568',
    textAlign: 'center',
  },
  letterContainer: {
    backgroundColor: 'white',
    borderRadius: 20,
    padding: 24,
    marginBottom: 24,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 8,
    },
    shadowOpacity: 0.15,
    shadowRadius: 16,
    elevation: 8,
    borderWidth: 2,
    borderColor: '#000000ff',
  },
  letterHeader: {
    alignItems: 'center',
    marginBottom: 24,
  },
  letterTitle: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#2d3748',
    textAlign: 'center',
    marginBottom: 12,
  },
  decorativeLine: {
    width: 60,
    height: 3,
    backgroundColor: '#020200ff',
    borderRadius: 2,
  },
  letterBody: {
    marginBottom: 24,
  },
  letterContent: {
    fontSize: 18,
    color: '#4a5568',
    lineHeight: 28,
    textAlign: 'center',
    fontStyle: 'italic',
  },
  letterFooter: {
    alignItems: 'center',
  },
  completionDate: {
    fontSize: 14,
    color: '#000000ff',
    marginBottom: 12,
  },
  heartContainer: {
    alignItems: 'center',
  },
  heartIcon: {
    fontSize: 32,
  },
  messageContainer: {
    backgroundColor: '#d6dce0ff',
    borderRadius: 16,
    padding: 20,
    alignItems: 'center',
  },
  messageIcon: {
    fontSize: 32,
    marginBottom: 12,
  },
  messageText: {
    fontSize: 14,
    color: '#2d3748',
    textAlign: 'center',
    lineHeight: 20,
  },
  shareButton: {
    backgroundColor: '#000000ff',
    borderRadius: 12,
    padding: 14,
    alignItems: 'center',
    marginTop: 16,
    shadowColor: '#10b981',
    shadowOffset: {
      width: 0,
      height: 4,
    },
    shadowOpacity: 0.2,
    shadowRadius: 8,
    elevation: 4,
  },
  shareButtonText: {
    color: 'white',
    fontSize: 15,
    fontWeight: 'bold',
  },
});

export default FinalLetter;