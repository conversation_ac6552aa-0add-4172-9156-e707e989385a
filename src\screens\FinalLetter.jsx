import React, { useEffect, useState } from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  StyleSheet,
  ScrollView,
  Animated,
  Dimensions,
} from 'react-native';

const { width } = Dimensions.get('window');

const FinalLetter = ({ navigation, route }) => {
  const { letter } = route.params;
  const [fadeAnim] = useState(new Animated.Value(0));
  const [scaleAnim] = useState(new Animated.Value(0.8));
  const [heartAnim] = useState(new Animated.Value(0));

  useEffect(() => {
    // Animasyonları başlat
    Animated.sequence([
      Animated.parallel([
        Animated.timing(fadeAnim, {
          toValue: 1,
          duration: 1000,
          useNativeDriver: true,
        }),
        Animated.spring(scaleAnim, {
          toValue: 1,
          tension: 50,
          friction: 7,
          useNativeDriver: true,
        }),
      ]),
      Animated.loop(
        Animated.sequence([
          Animated.timing(heartAnim, {
            toValue: 1,
            duration: 1000,
            useNativeDriver: true,
          }),
          Animated.timing(heartAnim, {
            toValue: 0,
            duration: 1000,
            useNativeDriver: true,
          }),
        ])
      ),
    ]).start();
  }, []);

  const heartScale = heartAnim.interpolate({
    inputRange: [0, 1],
    outputRange: [1, 1.2],
  });

  const heartOpacity = heartAnim.interpolate({
    inputRange: [0, 0.5, 1],
    outputRange: [0.7, 1, 0.7],
  });

  return (
    <View style={styles.container}>
      <ScrollView 
        contentContainerStyle={styles.scrollContainer}
        showsVerticalScrollIndicator={false}
      >
        <Animated.View 
          style={[
            styles.congratsContainer,
            {
              opacity: fadeAnim,
              transform: [{ scale: scaleAnim }],
            },
          ]}
        >
          <Animated.Text 
            style={[
              styles.congratsIcon,
              {
                transform: [{ scale: heartScale }],
                opacity: heartOpacity,
              },
            ]}
          >
            🎉
          </Animated.Text>
          <Text style={styles.congratsTitle}>Tebrikler!</Text>
          <Text style={styles.congratsSubtitle}>
            Mektubu Başarıyla Çözdün!
          </Text>
        </Animated.View>

        <Animated.View 
          style={[
            styles.letterContainer,
            {
              opacity: fadeAnim,
              transform: [{ scale: scaleAnim }],
            },
          ]}
        >
          <View style={styles.letterHeader}>
            <Text style={styles.letterTitle}>
              {letter.finalLetter?.title || letter.title}
            </Text>
            <View style={styles.decorativeLine} />
          </View>

          <View style={styles.letterBody}>
            <Text style={styles.letterContent}>
              {letter.finalLetter?.content || 'Bu özel mektup sizin için hazırlandı.'}
            </Text>
          </View>

          <View style={styles.letterFooter}>
            <Text style={styles.completionDate}>
              {new Date().toLocaleDateString('tr-TR', {
                day: 'numeric',
                month: 'long',
                year: 'numeric',
              })}
            </Text>
            <View style={styles.heartContainer}>
              <Animated.Text 
                style={[
                  styles.heartIcon,
                  {
                    transform: [{ scale: heartScale }],
                    opacity: heartOpacity,
                  },
                ]}
              >
                💖
              </Animated.Text>
            </View>
          </View>
        </Animated.View>

        <View style={styles.statsContainer}>
          <Text style={styles.statsTitle}>📊 İstatistikler</Text>
          <View style={styles.statsGrid}>
            <View style={styles.statItem}>
              <Text style={styles.statNumber}>{letter.stages?.length || 0}</Text>
              <Text style={styles.statLabel}>Aşama</Text>
            </View>
            <View style={styles.statItem}>
              <Text style={styles.statNumber}>
                {letter.type === 'question' ? '🔐' : '🔍'}
              </Text>
              <Text style={styles.statLabel}>
                {letter.type === 'question' ? 'Şifreli' : 'Gizem'}
              </Text>
            </View>
            <View style={styles.statItem}>
              <Text style={styles.statNumber}>✅</Text>
              <Text style={styles.statLabel}>Tamamlandı</Text>
            </View>
          </View>
        </View>

        <View style={styles.messageContainer}>
          <Text style={styles.messageIcon}>💌</Text>
          <Text style={styles.messageText}>
            Bu özel mektup sadece sizin için hazırlandı. 
            Tüm aşamaları başarıyla çözerek buraya ulaştınız!
          </Text>
        </View>
      </ScrollView>

      <View style={styles.bottomContainer}>
        <TouchableOpacity
          style={styles.homeButton}
          onPress={() => navigation.navigate('Home')}
        >
          <Text style={styles.homeButtonText}>🏠 Ana Sayfaya Dön</Text>
        </TouchableOpacity>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#fef5e7',
  },
  scrollContainer: {
    padding: 20,
    paddingBottom: 100,
  },
  congratsContainer: {
    alignItems: 'center',
    marginBottom: 30,
  },
  congratsIcon: {
    fontSize: 64,
    marginBottom: 16,
  },
  congratsTitle: {
    fontSize: 32,
    fontWeight: 'bold',
    color: '#2d3748',
    marginBottom: 8,
    textAlign: 'center',
  },
  congratsSubtitle: {
    fontSize: 18,
    color: '#4a5568',
    textAlign: 'center',
  },
  letterContainer: {
    backgroundColor: 'white',
    borderRadius: 20,
    padding: 24,
    marginBottom: 24,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 8,
    },
    shadowOpacity: 0.15,
    shadowRadius: 16,
    elevation: 8,
    borderWidth: 2,
    borderColor: '#f6e05e',
  },
  letterHeader: {
    alignItems: 'center',
    marginBottom: 24,
  },
  letterTitle: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#2d3748',
    textAlign: 'center',
    marginBottom: 12,
  },
  decorativeLine: {
    width: 60,
    height: 3,
    backgroundColor: '#f6e05e',
    borderRadius: 2,
  },
  letterBody: {
    marginBottom: 24,
  },
  letterContent: {
    fontSize: 18,
    color: '#4a5568',
    lineHeight: 28,
    textAlign: 'center',
    fontStyle: 'italic',
  },
  letterFooter: {
    alignItems: 'center',
  },
  completionDate: {
    fontSize: 14,
    color: '#718096',
    marginBottom: 12,
  },
  heartContainer: {
    alignItems: 'center',
  },
  heartIcon: {
    fontSize: 32,
  },
  statsContainer: {
    backgroundColor: 'white',
    borderRadius: 16,
    padding: 20,
    marginBottom: 20,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 4,
  },
  statsTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#2d3748',
    textAlign: 'center',
    marginBottom: 16,
  },
  statsGrid: {
    flexDirection: 'row',
    justifyContent: 'space-around',
  },
  statItem: {
    alignItems: 'center',
  },
  statNumber: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#4299e1',
    marginBottom: 4,
  },
  statLabel: {
    fontSize: 12,
    color: '#718096',
    textAlign: 'center',
  },
  messageContainer: {
    backgroundColor: '#e6fffa',
    borderRadius: 16,
    padding: 20,
    alignItems: 'center',
  },
  messageIcon: {
    fontSize: 32,
    marginBottom: 12,
  },
  messageText: {
    fontSize: 14,
    color: '#2d3748',
    textAlign: 'center',
    lineHeight: 20,
  },
  bottomContainer: {
    position: 'absolute',
    bottom: 0,
    left: 0,
    right: 0,
    backgroundColor: 'white',
    padding: 20,
    borderTopWidth: 1,
    borderTopColor: '#e2e8f0',
  },
  homeButton: {
    backgroundColor: '#4299e1',
    borderRadius: 16,
    padding: 16,
    alignItems: 'center',
  },
  homeButtonText: {
    color: 'white',
    fontSize: 16,
    fontWeight: '600',
  },
});

export default FinalLetter;
