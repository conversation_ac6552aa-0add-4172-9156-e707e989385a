import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  StyleSheet,
  FlatList,
  Alert,
} from 'react-native';
import { collection, query, onSnapshot, orderBy } from 'firebase/firestore';
import { db } from '../config/firebase';
import LetterCard from '../components/LetterCard';

const HomePage = ({ navigation }) => {
  const [letters, setLetters] = useState([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    // Firebase'den tüm mektupları dinle (authentication olmadan)
    const lettersQuery = query(
      collection(db, 'letters'),
      orderBy('createdAt', 'desc')
    );

    const unsubscribe = onSnapshot(lettersQuery, (snapshot) => {
      const lettersData = snapshot.docs.map(doc => ({
        id: doc.id,
        ...doc.data()
      }));
      setLetters(lettersData);
      setLoading(false);
    });

    return () => unsubscribe();
  }, []);



  const handleLetterPress = (letter) => {
    navigation.navigate('LetterPlay', { letterId: letter.id });
  };

  const renderEmptyState = () => (
    <View style={styles.emptyContainer}>
      <Text style={styles.emptyIcon}>📝</Text>
      <Text style={styles.emptyTitle}>Henüz mektup yok</Text>
      <Text style={styles.emptySubtitle}>
        İlk mektubunuzu oluşturmak için '+' butonuna tıklayın
      </Text>
    </View>
  );

  const renderLetterItem = ({ item }) => (
    <LetterCard
      letter={item}
      onPress={() => handleLetterPress(item)}
    />
  );

  return (
    <View style={styles.container}>
      <FlatList
        data={letters}
        renderItem={renderLetterItem}
        keyExtractor={(item) => item.id}
        ListHeaderComponent={() => (
          <View style={styles.header}>
            <View style={styles.headerContent}>
              <Text style={styles.headerIcon}>💌</Text>
              <Text style={styles.headerTitle}>Mektuplarım</Text>
            </View>
            <Text style={styles.headerSubtitle}>
              Sevdikleriniz için oluşturduğunuz özel mektuplar
            </Text>
          </View>
        )}
        contentContainerStyle={[styles.listContainer, { paddingBottom: 200 }]}
        ListEmptyComponent={!loading ? renderEmptyState : null}
        showsVerticalScrollIndicator={false}
      />

      <TouchableOpacity
        style={styles.addButton}
        onPress={() => navigation.navigate('Creator')}
      >
        <Text style={styles.addButtonText}>+</Text>
      </TouchableOpacity>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f0f4f8',
  },
  header: {
    paddingTop: 40,
    paddingBottom: 30,
    paddingHorizontal: 20,
    alignItems: 'center',
    backgroundColor: '#667eea',
    borderBottomLeftRadius: 30,
    borderBottomRightRadius: 30,
    marginHorizontal: -20,
    marginTop: -20,
    marginBottom: 24,
  },
  headerContent: {
    alignItems: 'center',
    marginBottom: 12,
  },
  headerIcon: {
    fontSize: 48,
    marginBottom: 8,
  },
  headerTitle: {
    fontSize: 28,
    fontWeight: 'bold',
    color: 'white',
    textShadowColor: 'rgba(0, 0, 0, 0.3)',
    textShadowOffset: { width: 1, height: 1 },
    textShadowRadius: 2,
  },
  headerSubtitle: {
    fontSize: 16,
    color: 'rgba(255, 255, 255, 0.9)',
    textAlign: 'center',
    lineHeight: 22,
  },
  listContainer: {
    padding: 20,
    flexGrow: 1,
  },
  emptyContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 40,
  },
  emptyIcon: {
    fontSize: 64,
    marginBottom: 16,
  },
  emptyTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#2d3748',
    marginBottom: 8,
    textAlign: 'center',
  },
  emptySubtitle: {
    fontSize: 16,
    color: '#718096',
    textAlign: 'center',
    lineHeight: 24,
  },
  addButton: {
    position: 'absolute',
    bottom: 30,
    right: 30,
    width: 60,
    height: 60,
    borderRadius: 30,
    backgroundColor: '#4299e1',
    justifyContent: 'center',
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 4,
    },
    shadowOpacity: 0.3,
    shadowRadius: 8,
    elevation: 8,
  },
  addButtonText: {
    fontSize: 28,
    color: 'white',
    fontWeight: 'bold',
  },
});

export default HomePage;
