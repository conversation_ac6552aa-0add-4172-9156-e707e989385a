import React, { useState } from 'react';
import {
  View,
  Text,
  TextInput,
  TouchableOpacity,
  StyleSheet,
  ScrollView,
  Alert,
  KeyboardAvoidingView,
  Platform,
} from 'react-native';
import { collection, addDoc, serverTimestamp } from 'firebase/firestore';
import { db } from '../config/firebase';

const FinalLetterEditor = ({ navigation, route }) => {
  const { stages, type } = route.params;
  const [title, setTitle] = useState('');
  const [content, setContent] = useState('');
  const [loading, setLoading] = useState(false);

  const handleSaveLetter = async () => {
    if (!title.trim()) {
      Alert.alert('Eksik Bilgi', 'Lütfen mektup başlığını girin.');
      return;
    }

    if (!content.trim()) {
      Alert.alert('Eksik Bilgi', 'Lütfen mektup içeriğini yazın.');
      return;
    }

    setLoading(true);

    try {
      const letterData = {
        userId: 'anonymous-user', // Sabit kullanıcı ID'si
        title: title.trim(),
        type,
        stages,
        finalLetter: {
          title: title.trim(),
          content: content.trim(),
        },
        currentStage: 0,
        isCompleted: false,
        createdAt: serverTimestamp(),
        updatedAt: serverTimestamp(),
      };

      await addDoc(collection(db, 'letters'), letterData);

      Alert.alert(
        'Başarılı!',
        'Mektubunuz başarıyla oluşturuldu.',
        [
          {
            text: 'Tamam',
            onPress: () => navigation.navigate('Home'),
          },
        ]
      );
    } catch (error) {
      console.error('Error saving letter:', error);
      Alert.alert('Hata', 'Mektup kaydedilirken bir hata oluştu.');
    } finally {
      setLoading(false);
    }
  };

  const getSummaryText = () => {
    const stageCount = stages.length;
    const typeText = type === 'question' ? 'Şifreli Soru' : 'Dedektiflik Gizemi';
    return `${stageCount} aşamalı ${typeText}`;
  };

  return (
    <KeyboardAvoidingView
      style={styles.container}
      behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
    >
      <ScrollView style={styles.scrollContainer} showsVerticalScrollIndicator={false}>
        <View style={styles.header}>
          <View style={styles.headerContent}>
            <Text style={styles.headerIcon}>💌</Text>
            <Text style={styles.title}>Son Mektubu Yaz</Text>
          </View>
          <Text style={styles.subtitle}>
            Tüm aşamalar çözüldüğünde görülecek özel mesajınızı yazın
          </Text>
        </View>

        <View style={styles.summaryContainer}>
          <Text style={styles.summaryTitle}>📋 Mektup Özeti</Text>
          <Text style={styles.summaryText}>{getSummaryText()}</Text>
          <View style={styles.stagesList}>
            {stages.map((stage, index) => (
              <View key={index} style={styles.stageItem}>
                <Text style={styles.stageNumber}>{index + 1}</Text>
                <Text style={styles.stageDescription}>
                  {type === 'question' 
                    ? stage.question.substring(0, 50) + (stage.question.length > 50 ? '...' : '')
                    : stage.story.substring(0, 50) + (stage.story.length > 50 ? '...' : '')
                  }
                </Text>
              </View>
            ))}
          </View>
        </View>

        <View style={styles.formContainer}>
          <View style={styles.inputGroup}>
            <Text style={styles.label}>Mektup Başlığı *</Text>
            <TextInput
              style={styles.input}
              placeholder="Mektubunuzun başlığı (örn: Sana Aşk Mektubum)"
              value={title}
              onChangeText={setTitle}
            />
          </View>

          <View style={styles.inputGroup}>
            <Text style={styles.label}>Mektup İçeriği *</Text>
            <Text style={styles.helperText}>
              Bu mesaj, tüm aşamalar çözüldüğünde görülecek özel içeriktir
            </Text>
            <TextInput
              style={[styles.textArea, styles.contentInput]}
              placeholder="Tüm aşamalar çözüldüğünde görülecek özel mesajını buraya yaz..."
              value={content}
              onChangeText={setContent}
              multiline
              numberOfLines={8}
              textAlignVertical="top"
            />
          </View>

          <View style={styles.tipContainer}>
            <Text style={styles.tipIcon}>💡</Text>
            <Text style={styles.tipText}>
              Bu mektup sadece tüm aşamalar başarıyla çözüldüğünde görünecek. 
              Duygusal ve kişisel bir mesaj yazabilirsiniz.
            </Text>
          </View>

          <TouchableOpacity
            style={[styles.saveButton, loading && styles.disabledButton]}
            onPress={handleSaveLetter}
            disabled={loading}
          >
            <Text style={styles.saveButtonText}>
              {loading ? 'Kaydediliyor...' : '💌 Mektubu Kaydet'}
            </Text>
          </TouchableOpacity>
        </View>

        <View style={styles.previewContainer}>
          <Text style={styles.previewTitle}>📖 Önizleme</Text>
          <View style={styles.previewCard}>
            <Text style={styles.previewCardTitle}>
              {title || 'Mektup Başlığı'}
            </Text>
            <Text style={styles.previewCardContent}>
              {content || 'Mektup içeriği burada görünecek...'}
            </Text>
          </View>
        </View>

        {/* Alt boşluk */}
        <View style={styles.bottomSpacing} />
      </ScrollView>
    </KeyboardAvoidingView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f0f4f8',
  },
  scrollContainer: {
    flex: 1,
    paddingHorizontal: 20,
    paddingBottom: 100,
  },
  header: {
    paddingTop: 50,
    paddingBottom: 30,
    paddingHorizontal: 0,
    alignItems: 'center',
    backgroundColor: '#667eea',
    borderBottomLeftRadius: 30,
    borderBottomRightRadius: 30,
    marginHorizontal: 0,
    marginBottom: 24,
  },
  headerContent: {
    alignItems: 'center',
    marginBottom: 12,
  },
  headerIcon: {
    fontSize: 48,
    marginBottom: 8,
  },
  title: {
    fontSize: 28,
    fontWeight: 'bold',
    color: 'white',
    marginBottom: 8,
    textAlign: 'center',
    textShadowColor: 'rgba(0, 0, 0, 0.3)',
    textShadowOffset: { width: 1, height: 1 },
    textShadowRadius: 2,
  },
  subtitle: {
    fontSize: 16,
    color: 'rgba(255, 255, 255, 0.9)',
    textAlign: 'center',
    lineHeight: 24,
  },
  bottomSpacing: {
    height: 200,
  },
  summaryContainer: {
    backgroundColor: 'white',
    margin: 20,
    borderRadius: 16,
    padding: 20,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 4,
  },
  summaryTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#2d3748',
    marginBottom: 8,
  },
  summaryText: {
    fontSize: 16,
    color: '#4299e1',
    fontWeight: '600',
    marginBottom: 16,
  },
  stagesList: {
    marginTop: 8,
  },
  stageItem: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8,
  },
  stageNumber: {
    width: 24,
    height: 24,
    borderRadius: 12,
    backgroundColor: '#4299e1',
    color: 'white',
    textAlign: 'center',
    fontSize: 12,
    fontWeight: 'bold',
    lineHeight: 24,
    marginRight: 12,
  },
  stageDescription: {
    flex: 1,
    fontSize: 14,
    color: '#718096',
  },
  formContainer: {
    backgroundColor: 'white',
    margin: 20,
    marginTop: 0,
    borderRadius: 16,
    padding: 20,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 4,
  },
  inputGroup: {
    marginBottom: 20,
  },
  label: {
    fontSize: 16,
    fontWeight: '600',
    color: '#2d3748',
    marginBottom: 8,
  },
  helperText: {
    fontSize: 14,
    color: '#718096',
    marginBottom: 8,
  },
  input: {
    borderWidth: 1,
    borderColor: '#e2e8f0',
    borderRadius: 12,
    padding: 16,
    fontSize: 16,
    backgroundColor: '#f7fafc',
  },
  textArea: {
    borderWidth: 1,
    borderColor: '#e2e8f0',
    borderRadius: 12,
    padding: 16,
    fontSize: 16,
    backgroundColor: '#f7fafc',
  },
  contentInput: {
    minHeight: 150,
  },
  tipContainer: {
    flexDirection: 'row',
    backgroundColor: '#e6fffa',
    borderRadius: 12,
    padding: 16,
    alignItems: 'flex-start',
    marginBottom: 20,
  },
  tipIcon: {
    fontSize: 20,
    marginRight: 12,
  },
  tipText: {
    flex: 1,
    fontSize: 14,
    color: '#2d3748',
    lineHeight: 20,
  },
  saveButton: {
    backgroundColor: '#48bb78',
    borderRadius: 12,
    padding: 16,
    alignItems: 'center',
  },
  saveButtonText: {
    color: 'white',
    fontSize: 16,
    fontWeight: '600',
  },
  disabledButton: {
    opacity: 0.6,
  },
  previewContainer: {
    margin: 20,
    marginTop: 0,
  },
  previewTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#2d3748',
    marginBottom: 16,
  },
  previewCard: {
    backgroundColor: '#fef5e7',
    borderRadius: 16,
    padding: 20,
    borderWidth: 2,
    borderColor: '#f6e05e',
  },
  previewCardTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#2d3748',
    marginBottom: 12,
    textAlign: 'center',
  },
  previewCardContent: {
    fontSize: 16,
    color: '#4a5568',
    lineHeight: 24,
    textAlign: 'center',
  },
});

export default FinalLetterEditor;
