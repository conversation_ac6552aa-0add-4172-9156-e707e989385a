import React, { useState } from 'react';
import {
  View,
  Text,
  TextInput,
  TouchableOpacity,
  StyleSheet,
  ScrollView,
  Alert,
  KeyboardAvoidingView,
  Platform,
} from 'react-native';
import { collection, addDoc, serverTimestamp } from 'firebase/firestore';
import { db } from '../config/firebase';

const FinalLetterEditor = ({ navigation, route }) => {
  const { stages, type } = route.params;
  const [title, setTitle] = useState('');
  const [content, setContent] = useState('');
  const [loading, setLoading] = useState(false);

  const handleSaveLetter = async () => {
    if (!title.trim()) {
      Alert.alert('Eksik Bilgi', 'Lütfen mektup başlığını girin.');
      return;
    }

    if (!content.trim()) {
      Alert.alert('Eksik Bilgi', 'Lütfen mektup içeriğini yazın.');
      return;
    }

    setLoading(true);

    try {
      const letterData = {
        userId: 'anonymous-user',
        title: title.trim(),
        type,
        stages,
        finalLetter: {
          title: title.trim(),
          content: content.trim(),
        },
        currentStage: 0,
        isCompleted: false,
        createdAt: serverTimestamp(),
        updatedAt: serverTimestamp(),
      };

      await addDoc(collection(db, 'letters'), letterData);

      Alert.alert(
        'Başarılı!',
        'Mektubunuz başarıyla oluşturuldu.',
        [
          {
            text: 'Tamam',
            onPress: () => navigation.navigate('Home'),
          },
        ]
      );
    } catch (error) {
      console.error('Error saving letter:', error);
      Alert.alert('Hata', 'Mektup kaydedilirken bir hata oluştu.');
    } finally {
      setLoading(false);
    }
  };

  const getSummaryText = () => {
    const stageCount = stages.length;
    const typeText = type === 'question' ? 'Şifreli Soru' : 'Dedektiflik Gizemi';
    return `${stageCount} aşamalı ${typeText}`;
  };

  return (
    <KeyboardAvoidingView
      style={styles.container}
      behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
    >
      <ScrollView style={styles.scrollContainer} contentContainerStyle={styles.contentContainer} showsVerticalScrollIndicator={false}>
        <View style={styles.header}>
            <Text style={styles.headerIcon}>💌</Text>
            <Text style={styles.title}>Son Mektubu Yaz</Text>
            <Text style={styles.subtitle}>
                Tüm aşamalar çözüldüğünde görülecek özel mesajınızı yazın
            </Text>
        </View>

        <View style={styles.card}>
            <Text style={styles.cardTitle}>Mektup Özeti</Text>
            <Text style={styles.summaryText}>{getSummaryText()}</Text>
            <View style={styles.stagesList}>
                {stages.map((stage, index) => (
                <View key={index} style={styles.stageItem}>
                    <Text style={styles.stageNumber}>{index + 1}</Text>
                    <Text style={styles.stageDescription} numberOfLines={1}>
                    {type === 'question' 
                        ? stage.question
                        : stage.story
                    }
                    </Text>
                </View>
                ))}
            </View>
        </View>

        <View style={styles.card}>
            <Text style={styles.cardTitle}>Mektup Detayları</Text>
            <View style={styles.inputGroup}>
                <Text style={styles.label}>Mektup Başlığı *</Text>
                <TextInput
                style={styles.input}
                placeholder="Mektubunuzun başlığı..."
                value={title}
                onChangeText={setTitle}
                />
            </View>

            <View style={styles.inputGroup}>
                <Text style={styles.label}>Mektup İçeriği *</Text>
                <TextInput
                style={styles.textArea}
                placeholder="Özel mesajınızı buraya yazın..."
                value={content}
                onChangeText={setContent}
                multiline
                textAlignVertical="top"
                />
            </View>
        </View>

        <View style={styles.card}>
            <Text style={styles.cardTitle}>Önizleme</Text>
            <View style={styles.previewCard}>
                <Text style={styles.previewCardTitle}>{title || 'Başlık'}</Text>
                <Text style={styles.previewCardContent}>{content || 'İçerik...'}</Text>
            </View>
        </View>

        <TouchableOpacity
            style={[styles.saveButton, loading && styles.disabledButton]}
            onPress={handleSaveLetter}
            disabled={loading}
            >
            <Text style={styles.saveButtonText}>
                {loading ? 'Kaydediliyor...' : 'Mektubu Kaydet ve Bitir'}
            </Text>
        </TouchableOpacity>

      </ScrollView>
    </KeyboardAvoidingView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f0f4f8',
  },
  scrollContainer: {
    flex: 1,
  },
  contentContainer: {
    paddingHorizontal: 20,
    paddingBottom: 120,
  },
  header: {
    paddingTop: 40,
    paddingBottom: 30,
    paddingHorizontal: 20,
    alignItems: 'center',
    backgroundColor: '#6366f1', // Indigo color
    borderBottomLeftRadius: 30,
    borderBottomRightRadius: 30,
    marginBottom: 24,
    marginHorizontal: -20, // Full-width header
  },
  headerIcon: {
    fontSize: 48,
    marginBottom: 8,
  },
  title: {
    fontSize: 28,
    fontWeight: 'bold',
    color: 'white',
    textAlign: 'center',
  },
  subtitle: {
    fontSize: 16,
    color: 'rgba(255, 255, 255, 0.9)',
    textAlign: 'center',
    marginTop: 8,
  },
  card: {
    backgroundColor: 'white',
    borderRadius: 16,
    padding: 20,
    marginBottom: 20,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 4,
  },
  cardTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#1e293b',
    marginBottom: 16,
  },
  summaryText: {
    fontSize: 16,
    color: '#6366f1',
    fontWeight: '600',
    marginBottom: 16,
  },
  stagesList: {
    marginTop: 8,
  },
  stageItem: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 10,
    backgroundColor: '#f8fafc',
    padding: 10,
    borderRadius: 8,
  },
  stageNumber: {
    width: 24,
    height: 24,
    borderRadius: 12,
    backgroundColor: '#6366f1',
    color: 'white',
    textAlign: 'center',
    lineHeight: 24,
    fontWeight: 'bold',
    marginRight: 12,
  },
  stageDescription: {
    flex: 1,
    fontSize: 14,
    color: '#475569',
  },
  inputGroup: {
    marginBottom: 20,
  },
  label: {
    fontSize: 16,
    fontWeight: '600',
    color: '#334155',
    marginBottom: 8,
  },
  input: {
    borderWidth: 1,
    borderColor: '#e2e8f0',
    borderRadius: 12,
    padding: 14,
    fontSize: 16,
    backgroundColor: '#f7fafc',
  },
  textArea: {
    borderWidth: 1,
    borderColor: '#e2e8f0',
    borderRadius: 12,
    padding: 14,
    fontSize: 16,
    backgroundColor: '#f7fafc',
    minHeight: 150,
  },
  previewCard: {
    backgroundColor: '#fefce8', // Light yellow
    borderRadius: 12,
    padding: 16,
    borderWidth: 1,
    borderColor: '#facc15', // Yellow
  },
  previewCardTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#422006',
    marginBottom: 8,
    textAlign: 'center',
  },
  previewCardContent: {
    fontSize: 16,
    color: '#78350f',
    lineHeight: 24,
    textAlign: 'center',
  },
  saveButton: {
    backgroundColor: '#22c55e', // Green
    borderRadius: 12,
    padding: 18,
    alignItems: 'center',
    marginTop: 8,
  },
  saveButtonText: {
    color: 'white',
    fontSize: 18,
    fontWeight: 'bold',
  },
  disabledButton: {
    opacity: 0.6,
  },
});

export default FinalLetterEditor;