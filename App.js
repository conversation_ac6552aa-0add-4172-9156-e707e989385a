import React, { useEffect, useState } from 'react';
import { StatusBar } from 'expo-status-bar';
import { StyleSheet, Image, Animated } from 'react-native';
import { GestureHandlerRootView } from 'react-native-gesture-handler';
import * as SplashScreen from 'expo-splash-screen';
import AppNavigator from './src/navigation/AppNavigator';

// Splash screen'i manuel olarak gizlemeyi engelle
SplashScreen.preventAutoHideAsync();

export default function App() {
  const [showCustomSplash, setShowCustomSplash] = useState(true);
  const fadeAnim = new Animated.Value(1);

  useEffect(() => {
    async function prepare() {
      try {
        // Uygulama yükleme simülasyonu (2.5 saniye)
        await new Promise(resolve => setTimeout(resolve, 2500));

        // Custom splash screen'i fade out yap
        Animated.timing(fadeAnim, {
          toValue: 0,
          duration: 500,
          useNativeDriver: true,
        }).start(() => {
          setShowCustomSplash(false);
        });

        // Native splash screen'i gizle
        await SplashScreen.hideAsync();
      } catch (e) {
        console.warn(e);
        setShowCustomSplash(false);
        await SplashScreen.hideAsync();
      }
    }

    prepare();
  }, []);

  if (showCustomSplash) {
    return (
      <Animated.View style={[styles.splashContainer, { opacity: fadeAnim }]}>
        <Image
          source={require('./assets/splashscreen.jpg')}
          style={styles.splashImage}
          resizeMode="cover"
        />
      </Animated.View>
    );
  }

  return (
    <GestureHandlerRootView style={styles.container}>
      <AppNavigator />
      <StatusBar style="light" />
    </GestureHandlerRootView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  splashContainer: {
    flex: 1,
    backgroundColor: '#667eea',
  },
  splashImage: {
    width: '100%',
    height: '100%',
  },
});
