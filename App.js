import React, { useEffect, useState } from 'react';
import { StatusBar } from 'expo-status-bar';
import { StyleSheet, View, Image, Text, Animated } from 'react-native';
import { GestureHandlerRootView } from 'react-native-gesture-handler';
import * as SplashScreen from 'expo-splash-screen';
import AppNavigator from './src/navigation/AppNavigator';

// Splash screen'i manuel olarak gizlemeyi engelle
SplashScreen.preventAutoHideAsync();

export default function App() {
  const [isLoading, setIsLoading] = useState(true);
  const [showCustomSplash, setShowCustomSplash] = useState(true);
  const fadeAnim = new Animated.Value(1);
  const dotAnim1 = new Animated.Value(0.3);
  const dotAnim2 = new Animated.Value(0.3);
  const dotAnim3 = new Animated.Value(0.3);

  useEffect(() => {
    // Loading dots animasyonu
    const animateDots = () => {
      const createAnimation = (animValue, delay) => {
        return Animated.loop(
          Animated.sequence([
            Animated.timing(animValue, {
              toValue: 1,
              duration: 600,
              delay,
              useNativeDriver: true,
            }),
            Animated.timing(animValue, {
              toValue: 0.3,
              duration: 600,
              useNativeDriver: true,
            }),
          ])
        );
      };

      Animated.parallel([
        createAnimation(dotAnim1, 0),
        createAnimation(dotAnim2, 200),
        createAnimation(dotAnim3, 400),
      ]).start();
    };

    animateDots();

    async function prepare() {
      try {
        // Uygulama yükleme simülasyonu (3 saniye)
        await new Promise(resolve => setTimeout(resolve, 3000));

        // Custom splash screen'i fade out yap
        Animated.timing(fadeAnim, {
          toValue: 0,
          duration: 800,
          useNativeDriver: true,
        }).start(() => {
          setShowCustomSplash(false);
          setIsLoading(false);
        });

        // Native splash screen'i gizle
        await SplashScreen.hideAsync();
      } catch (e) {
        console.warn(e);
        setIsLoading(false);
        setShowCustomSplash(false);
        await SplashScreen.hideAsync();
      }
    }

    prepare();
  }, []);

  if (showCustomSplash) {
    return (
      <Animated.View style={[styles.splashContainer, { opacity: fadeAnim }]}>
        <View style={styles.splashContent}>
          <Image
            source={require('./assets/splashscreen.jpg')}
            style={styles.splashImage}
            resizeMode="cover"
          />
          <View style={styles.splashOverlay}>
            <Text style={styles.splashTitle}>Mystery Letter</Text>
            <Text style={styles.splashSubtitle}>Sevdikleriniz için özel mektuplar</Text>
            <View style={styles.loadingContainer}>
              <Animated.View style={[styles.loadingDot, { opacity: dotAnim1 }]} />
              <Animated.View style={[styles.loadingDot, { opacity: dotAnim2 }]} />
              <Animated.View style={[styles.loadingDot, { opacity: dotAnim3 }]} />
            </View>
          </View>
        </View>
      </Animated.View>
    );
  }

  return (
    <GestureHandlerRootView style={styles.container}>
      <AppNavigator />
      <StatusBar style="light" />
    </GestureHandlerRootView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  splashContainer: {
    flex: 1,
    backgroundColor: '#667eea',
  },
  splashContent: {
    flex: 1,
    position: 'relative',
  },
  splashImage: {
    width: '100%',
    height: '100%',
    position: 'absolute',
  },
  splashOverlay: {
    flex: 1,
    backgroundColor: 'rgba(102, 126, 234, 0.8)',
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 40,
  },
  splashTitle: {
    fontSize: 36,
    fontWeight: 'bold',
    color: 'white',
    textAlign: 'center',
    marginBottom: 12,
    textShadowColor: 'rgba(0, 0, 0, 0.5)',
    textShadowOffset: { width: 2, height: 2 },
    textShadowRadius: 4,
  },
  splashSubtitle: {
    fontSize: 18,
    color: 'rgba(255, 255, 255, 0.9)',
    textAlign: 'center',
    marginBottom: 40,
    lineHeight: 24,
    textShadowColor: 'rgba(0, 0, 0, 0.3)',
    textShadowOffset: { width: 1, height: 1 },
    textShadowRadius: 2,
  },
  loadingContainer: {
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingDot: {
    width: 12,
    height: 12,
    borderRadius: 6,
    backgroundColor: 'white',
    marginHorizontal: 4,
    opacity: 0.7,
  },
});
