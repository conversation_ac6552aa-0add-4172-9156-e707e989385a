import React, { useState } from 'react';
import {
  View,
  Text,
  TextInput,
  TouchableOpacity,
  StyleSheet,
  ScrollView,
} from 'react-native';

const QuestionStage = ({ stage, onAnswer, loading }) => {
  const [answer, setAnswer] = useState('');
  const [showHint, setShowHint] = useState(false);

  const handleSubmit = () => {
    if (!answer.trim()) {
      return;
    }
    onAnswer(answer.trim());
  };

  return (
    <ScrollView style={styles.container} contentContainerStyle={styles.contentContainer}>
      <View style={styles.questionContainer}>
        <Text style={styles.questionIcon}>❓</Text>
        <Text style={styles.questionText}>{stage.question}</Text>
      </View>

      <View style={styles.answerContainer}>
        <Text style={styles.answerLabel}>Cevabınız:</Text>
        <TextInput
          style={styles.answerInput}
          placeholder="Ceva<PERSON>ınızı buraya yazın"
          value={answer}
          onChangeText={setAnswer}
          editable={!loading}
          autoCapitalize="none"
          autoCorrect={false}
        />

        <TouchableOpacity
          style={[styles.submitButton, loading && styles.disabledButton]}
          onPress={handleSubmit}
          disabled={loading || !answer.trim()}
        >
          <Text style={styles.submitButtonText}>
            {loading ? 'Kontrol ediliyor...' : 'Cevapla'}
          </Text>
        </TouchableOpacity>
      </View>

      {stage.hint && (
        <View style={styles.hintContainer}>
          <TouchableOpacity
            style={styles.hintButton}
            onPress={() => setShowHint(!showHint)}
          >
            <Text style={styles.hintButtonText}>
              {showHint ? '🙈 İpucunu Gizle' : '💡 İpucu Göster'}
            </Text>
          </TouchableOpacity>

          {showHint && (
            <View style={styles.hintContent}>
              <Text style={styles.hintText}>{stage.hint}</Text>
            </View>
          )}
        </View>
      )}

      <View style={styles.tipContainer}>
        <Text style={styles.tipIcon}>ℹ️</Text>
        <Text style={styles.tipText}>
          Cevabınızı dikkatli bir şekilde kontrol edin. Yanlış cevap verirseniz mektup 24 saat kilitlenecek.
        </Text>
      </View>
    </ScrollView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f0f4f8',
  },
  contentContainer: {
    padding: 20,
  },
  questionContainer: {
    backgroundColor: 'white',
    borderRadius: 16,
    padding: 24,
    marginBottom: 20,
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 4,
  },
  questionIcon: {
    fontSize: 48,
    marginBottom: 16,
  },
  questionText: {
    fontSize: 18,
    color: '#2d3748',
    textAlign: 'center',
    lineHeight: 26,
  },
  answerContainer: {
    backgroundColor: 'white',
    borderRadius: 16,
    padding: 20,
    marginBottom: 20,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 4,
  },
  answerLabel: {
    fontSize: 16,
    fontWeight: '600',
    color: '#2d3748',
    marginBottom: 12,
  },
  answerInput: {
    borderWidth: 1,
    borderColor: '#e2e8f0',
    borderRadius: 12,
    padding: 16,
    fontSize: 16,
    backgroundColor: '#f7fafc',
    marginBottom: 16,
  },
  submitButton: {
    backgroundColor: '#4299e1',
    borderRadius: 12,
    padding: 16,
    alignItems: 'center',
  },
  submitButtonText: {
    color: 'white',
    fontSize: 16,
    fontWeight: '600',
  },
  disabledButton: {
    opacity: 0.6,
  },
  hintContainer: {
    backgroundColor: 'white',
    borderRadius: 16,
    padding: 20,
    marginBottom: 20,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 4,
  },
  hintButton: {
    backgroundColor: '#fef5e7',
    borderRadius: 12,
    padding: 12,
    alignItems: 'center',
    borderWidth: 1,
    borderColor: '#f6e05e',
  },
  hintButtonText: {
    color: '#744210',
    fontSize: 14,
    fontWeight: '600',
  },
  hintContent: {
    marginTop: 16,
    padding: 16,
    backgroundColor: '#fffbeb',
    borderRadius: 12,
    borderWidth: 1,
    borderColor: '#f6e05e',
  },
  hintText: {
    fontSize: 16,
    color: '#744210',
    textAlign: 'center',
    lineHeight: 24,
  },
  tipContainer: {
    flexDirection: 'row',
    backgroundColor: '#e6fffa',
    borderRadius: 12,
    padding: 16,
    alignItems: 'flex-start',
  },
  tipIcon: {
    fontSize: 20,
    marginRight: 12,
  },
  tipText: {
    flex: 1,
    fontSize: 14,
    color: '#2d3748',
    lineHeight: 20,
  },
});

export default QuestionStage;
