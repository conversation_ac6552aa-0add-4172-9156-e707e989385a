import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  Alert,
} from 'react-native';
import { doc, onSnapshot, getDoc, collection, query, where, getDocs } from 'firebase/firestore';
import { db } from '../config/firebase';
import StageManager from '../components/StageManager';

const LetterPlayPage = ({ navigation, route }) => {
  const { letterId } = route.params;
  const [letter, setLetter] = useState(null);
  const [letterAttempt, setLetterAttempt] = useState(null);
  const [loading, setLoading] = useState(true);
  const [timeRemaining, setTimeRemaining] = useState(0);

  useEffect(() => {
    // Mektubu dinle
    const letterUnsubscribe = onSnapshot(doc(db, 'letters', letterId), (doc) => {
      if (doc.exists()) {
        setLetter({ id: doc.id, ...doc.data() });
      } else {
        Alert.alert('Hata', 'Mektup bulunamadı.');
        navigation.goBack();
      }
    });

    // Letter attempt'i dinle (sabit kullanıcı ID'si ile)
    const fetchLetterAttempt = async () => {
      try {
        const attemptsQuery = query(
          collection(db, 'letterAttempts'),
          where('letterId', '==', letterId),
          where('userId', '==', 'anonymous-user')
        );

        const attemptsSnapshot = await getDocs(attemptsQuery);

        if (!attemptsSnapshot.empty) {
          const attemptDoc = attemptsSnapshot.docs[0];
          setLetterAttempt({ id: attemptDoc.id, ...attemptDoc.data() });
        }
      } catch (error) {
        console.error('Error fetching letter attempt:', error);
      } finally {
        setLoading(false);
      }
    };

    fetchLetterAttempt();

    return () => {
      letterUnsubscribe();
    };
  }, [letterId, navigation]);

  useEffect(() => {
    let interval;
    
    if (letterAttempt?.lockedUntil) {
      const lockDate = new Date(letterAttempt.lockedUntil.toDate());
      const now = new Date();
      
      if (lockDate > now) {
        const updateTimer = () => {
          const currentTime = new Date();
          const remaining = Math.max(0, lockDate - currentTime);
          setTimeRemaining(remaining);
          
          if (remaining === 0) {
            clearInterval(interval);
          }
        };
        
        updateTimer();
        interval = setInterval(updateTimer, 1000);
      }
    }

    return () => {
      if (interval) {
        clearInterval(interval);
      }
    };
  }, [letterAttempt]);

  const formatTimeRemaining = (milliseconds) => {
    const totalSeconds = Math.floor(milliseconds / 1000);
    const hours = Math.floor(totalSeconds / 3600);
    const minutes = Math.floor((totalSeconds % 3600) / 60);
    const seconds = totalSeconds % 60;

    if (hours > 0) {
      return `${hours} saat ${minutes} dakika`;
    } else if (minutes > 0) {
      return `${minutes} dakika ${seconds} saniye`;
    } else {
      return `${seconds} saniye`;
    }
  };

  const isLocked = () => {
    if (!letterAttempt?.lockedUntil) return false;
    const lockDate = new Date(letterAttempt.lockedUntil.toDate());
    return lockDate > new Date();
  };

  const renderLockedState = () => (
    <View style={styles.lockedContainer}>
      <Text style={styles.lockedIcon}>🔒</Text>
      <Text style={styles.lockedTitle}>Mektup Kilitli</Text>
      <Text style={styles.lockedMessage}>
        Bu mektup geçici olarak kilitlendi.
      </Text>
      <View style={styles.timerContainer}>
        <Text style={styles.timerText}>
          {formatTimeRemaining(timeRemaining)} sonra tekrar deneyebilirsiniz
        </Text>
      </View>
      <Text style={styles.lockedHint}>
        Yanlış cevap verdiğiniz için mektup kilitlendi. Lütfen bekleyin.
      </Text>
    </View>
  );

  if (loading) {
    return (
      <View style={styles.loadingContainer}>
        <Text style={styles.loadingText}>Mektup yükleniyor...</Text>
      </View>
    );
  }

  if (!letter) {
    return (
      <View style={styles.errorContainer}>
        <Text style={styles.errorText}>Mektup bulunamadı</Text>
      </View>
    );
  }

  // Mektup tamamlanmışsa final letter'a yönlendir
  if (letter.isCompleted) {
    navigation.replace('FinalLetter', { letter });
    return null;
  }

  return (
    <View style={styles.container}>
      <View style={styles.header}>
        <Text style={styles.letterTitle}>{letter.title}</Text>
        <Text style={styles.progressText}>
          Aşama {letter.currentStage + 1} / {letter.stages.length}
        </Text>
      </View>

      {isLocked() ? (
        renderLockedState()
      ) : (
        <StageManager
          letter={letter}
          letterAttempt={letterAttempt}
          navigation={navigation}
        />
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f0f4f8',
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#f0f4f8',
  },
  loadingText: {
    fontSize: 16,
    color: '#718096',
  },
  errorContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#f0f4f8',
  },
  errorText: {
    fontSize: 16,
    color: '#e53e3e',
  },
  header: {
    backgroundColor: 'white',
    padding: 20,
    borderBottomWidth: 1,
    borderBottomColor: '#e2e8f0',
    alignItems: 'center',
  },
  letterTitle: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#2d3748',
    marginBottom: 8,
    textAlign: 'center',
  },
  progressText: {
    fontSize: 16,
    color: '#4299e1',
    fontWeight: '600',
  },
  lockedContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 40,
  },
  lockedIcon: {
    fontSize: 64,
    marginBottom: 20,
  },
  lockedTitle: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#2d3748',
    marginBottom: 12,
    textAlign: 'center',
  },
  lockedMessage: {
    fontSize: 16,
    color: '#718096',
    textAlign: 'center',
    marginBottom: 20,
    lineHeight: 24,
  },
  timerContainer: {
    backgroundColor: '#fed7d7',
    borderRadius: 12,
    padding: 16,
    marginBottom: 20,
  },
  timerText: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#e53e3e',
    textAlign: 'center',
  },
  lockedHint: {
    fontSize: 14,
    color: '#a0aec0',
    textAlign: 'center',
    fontStyle: 'italic',
  },
});

export default LetterPlayPage;
