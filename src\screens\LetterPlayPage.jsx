import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  Alert,
  SafeAreaView,
  ScrollView,
} from 'react-native';
import { doc, onSnapshot, getDoc, collection, query, where, getDocs } from 'firebase/firestore';
import { db } from '../config/firebase';
import StageManager from '../components/StageManager';

const LetterPlayPage = ({ navigation, route }) => {
  const { letterId } = route.params;
  const [letter, setLetter] = useState(null);
  const [letterAttempt, setLetterAttempt] = useState(null);
  const [loading, setLoading] = useState(true);
  const [timeRemaining, setTimeRemaining] = useState(0);

  useEffect(() => {
    const letterUnsubscribe = onSnapshot(doc(db, 'letters', letterId), (doc) => {
      if (doc.exists()) {
        setLetter({ id: doc.id, ...doc.data() });
      } else {
        Alert.alert('Hata', 'Mektup bulunamadı.');
        navigation.goBack();
      }
    });

    const fetchLetterAttempt = async () => {
      try {
        const attemptsQuery = query(
          collection(db, 'letterAttempts'),
          where('letterId', '==', letterId),
          where('userId', '==', 'anonymous-user')
        );
        const attemptsSnapshot = await getDocs(attemptsQuery);
        if (!attemptsSnapshot.empty) {
          const attemptDoc = attemptsSnapshot.docs[0];
          setLetterAttempt({ id: attemptDoc.id, ...attemptDoc.data() });
        }
      } catch (error) {
        console.error('Error fetching letter attempt:', error);
      } finally {
        setLoading(false);
      }
    };

    fetchLetterAttempt();

    return () => letterUnsubscribe();
  }, [letterId, navigation]);

  useEffect(() => {
    let interval;
    if (letterAttempt?.lockedUntil) {
      const lockDate = new Date(letterAttempt.lockedUntil.toDate());
      const now = new Date();
      if (lockDate > now) {
        const updateTimer = () => {
          const currentTime = new Date();
          const remaining = Math.max(0, lockDate - currentTime);
          setTimeRemaining(remaining);
          if (remaining === 0) clearInterval(interval);
        };
        updateTimer();
        interval = setInterval(updateTimer, 1000);
      }
    }
    return () => clearInterval(interval);
  }, [letterAttempt]);

  useEffect(() => {
    if (letter?.isCompleted) {
      navigation.replace('FinalLetter', { letter });
    }
  }, [letter, navigation]);

  const formatTimeRemaining = (milliseconds) => {
    const totalSeconds = Math.floor(milliseconds / 1000);
    const hours = Math.floor(totalSeconds / 3600);
    const minutes = Math.floor((totalSeconds % 3600) / 60);
    const seconds = totalSeconds % 60;
    if (hours > 0) return `${hours} saat ${minutes} dakika`;
    if (minutes > 0) return `${minutes} dakika ${seconds} saniye`;
    return `${seconds} saniye`;
  };

  const isLocked = () => {
    if (!letterAttempt?.lockedUntil) return false;
    return new Date(letterAttempt.lockedUntil.toDate()) > new Date();
  };

  const renderLockedState = () => (
    <View style={styles.lockedContainer}>
      <Text style={styles.lockedIcon}>🔒</Text>
      <Text style={styles.lockedTitle}>Mektup Kilitli</Text>
      <Text style={styles.lockedMessage}>Bu mektup geçici olarak kilitlendi.</Text>
      <View style={styles.timerContainer}>
        <Text style={styles.timerText}>{formatTimeRemaining(timeRemaining)} sonra tekrar deneyebilirsiniz</Text>
      </View>
      <Text style={styles.lockedHint}>Yanlış cevap verdiğiniz için mektup kilitlendi. Lütfen bekleyin.</Text>
    </View>
  );

  if (loading) {
    return <View style={styles.loadingContainer}><Text style={styles.loadingText}>Mektup yükleniyor...</Text></View>;
  }

  if (!letter) {
    return <View style={styles.errorContainer}><Text style={styles.errorText}>Mektup bulunamadı</Text></View>;
  }

  if (letter.isCompleted) {
    return null;
  }

  return (
    <SafeAreaView style={styles.container}>
        <View style={styles.header}>
            <Text style={styles.headerIcon}>🕵️</Text>
            <View style={styles.headerTextContainer}>
                <Text style={styles.letterTitle} numberOfLines={1}>{letter.title}</Text>
                <Text style={styles.progressText}>Aşama {letter.currentStage + 1} / {letter.stages.length}</Text>
            </View>
        </View>
        
        <ScrollView style={styles.scrollContainer}>
            {isLocked() ? (
                renderLockedState()
            ) : (
                <StageManager letter={letter} letterAttempt={letterAttempt} navigation={navigation} />
            )}
        </ScrollView>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f0f4f8',
  },
  scrollContainer: {
      flex: 1,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    fontSize: 16,
    color: '#64748b',
  },
  errorContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  errorText: {
    fontSize: 16,
    color: '#ef4444',
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingTop: 30, // Increased padding to push it down
    paddingBottom: 16,
    paddingHorizontal: 16,
    backgroundColor: '#6d28d9', // Deeper purple
    borderBottomLeftRadius: 20,
    borderBottomRightRadius: 20,
    shadowColor: '#4c1d95',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.2,
    shadowRadius: 8,
    elevation: 5,
    justifyContent: 'center', // Center content horizontally
  },
  headerIcon: {
    fontSize: 32,
    marginRight: 12,
    color: 'white',
  },
  headerTextContainer: {
    alignItems: 'center', // Center text within its container
  },
  letterTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: 'white',
  },
  progressText: {
    fontSize: 14,
    color: 'rgba(255, 255, 255, 0.8)',
    marginTop: 2,
  },
  lockedContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 40,
    backgroundColor: '#f8fafc',
    minHeight: 500, // Ensure it takes space
  },
  lockedIcon: {
    fontSize: 64,
    marginBottom: 20,
  },
  lockedTitle: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#1e293b',
    marginBottom: 12,
    textAlign: 'center',
  },
  lockedMessage: {
    fontSize: 16,
    color: '#475569',
    textAlign: 'center',
    marginBottom: 24,
    lineHeight: 24,
  },
  timerContainer: {
    backgroundColor: '#fee2e2',
    borderRadius: 12,
    paddingVertical: 12,
    paddingHorizontal: 20,
    marginBottom: 24,
  },
  timerText: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#b91c1c',
    textAlign: 'center',
  },
  lockedHint: {
    fontSize: 14,
    color: '#94a3b8',
    textAlign: 'center',
    fontStyle: 'italic',
  },
});

export default LetterPlayPage;
