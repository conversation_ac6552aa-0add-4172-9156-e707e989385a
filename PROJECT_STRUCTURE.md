# 📁 Mystery Letter - <PERSON><PERSON>, Mystery Letter uygulamasının dosya yapısını ve her dosyanın işlevini açıklar.

## 🗂️ Ana Dizin Yap<PERSON>

```
MysteryLetter/
├── 📄 App.js                 # Ana uygulama bileşeni
├── 📄 README.md              # Proje açıklaması ve kullanım rehberi
├── 📄 SETUP.md               # Detaylı kurulum rehberi
├── 📄 PROJECT_STRUCTURE.md   # Bu dosya - proje yapısı
├── 📄 firestore.rules        # Firebase Firestore güvenlik kuralları
├── 📄 package.json           # NPM bağımlılıkları ve scriptler
├── 📄 package-lock.json      # NPM bağımlılık kilidi
├── 📄 app.json               # Expo uygulama yapılandırması
├── 📄 index.js               # Uygulama giriş noktası
├── 📁 assets/                # Statik dosyalar (ikonlar, resimler)
├── 📁 node_modules/          # NPM bağımlılıkları
└── 📁 src/                   # <PERSON><PERSON><PERSON> kod dizini
```

## 📂 src/ Dizini <PERSON>

```
src/
├── 📁 components/            # Yeniden kullanılabilir bileşenler
│   ├── 📄 LetterCard.jsx     # Mektup kartı bileşeni
│   ├── 📄 StageManager.jsx   # Aşama yönetici bileşeni
│   ├── 📄 QuestionStage.jsx  # Soru aşaması bileşeni
│   └── 📄 MysteryStage.jsx   # Gizem aşaması bileşeni
├── 📁 config/                # Yapılandırma dosyaları
│   └── 📄 firebase.js        # Firebase yapılandırması
├── 📁 navigation/            # Navigasyon yapılandırması
│   └── 📄 AppNavigator.js    # Ana navigasyon bileşeni
└── 📁 screens/               # Uygulama sayfaları
    ├── 📄 LoginPage.jsx      # Giriş sayfası
    ├── 📄 HomePage.jsx       # Ana sayfa (mektup listesi)
    ├── 📄 CreatorPage.jsx    # Mektup türü seçim sayfası
    ├── 📄 CreateQuestionStage.jsx  # Soru aşaması oluşturma
    ├── 📄 CreateMysteryStage.jsx   # Gizem aşaması oluşturma
    ├── 📄 FinalLetterEditor.jsx    # Son mektup editörü
    ├── 📄 LetterPlayPage.jsx       # Mektup çözme sayfası
    └── 📄 FinalLetter.jsx          # Başarı/tebrik sayfası
```

## 📄 Dosya Açıklamaları

### 🔧 Yapılandırma Dosyaları

- **`App.js`**: Ana uygulama bileşeni, navigasyonu başlatır
- **`package.json`**: NPM bağımlılıkları ve proje metadatası
- **`app.json`**: Expo uygulama yapılandırması
- **`firestore.rules`**: Firebase Firestore güvenlik kuralları

### 🧩 Bileşenler (components/)

- **`LetterCard.jsx`**: Ana sayfada mektupları gösteren kart bileşeni
- **`StageManager.jsx`**: Mektup aşamalarını yöneten ana bileşen
- **`QuestionStage.jsx`**: Soru-cevap aşamalarını gösteren bileşen
- **`MysteryStage.jsx`**: Gizem aşamalarını gösteren bileşen

### 📱 Sayfalar (screens/)

- **`LoginPage.jsx`**: Kullanıcı giriş sayfası
- **`HomePage.jsx`**: Mektup listesi ve ana sayfa
- **`CreatorPage.jsx`**: Mektup türü seçim sayfası
- **`CreateQuestionStage.jsx`**: Soru aşaması oluşturma sayfası
- **`CreateMysteryStage.jsx`**: Gizem aşaması oluşturma sayfası
- **`FinalLetterEditor.jsx`**: Son mektup yazma sayfası
- **`LetterPlayPage.jsx`**: Mektup çözme sayfası
- **`FinalLetter.jsx`**: Mektup tamamlandığında gösterilen sayfa

### ⚙️ Yapılandırma (config/)

- **`firebase.js`**: Firebase bağlantı ve yapılandırma ayarları

### 🧭 Navigasyon (navigation/)

- **`AppNavigator.js`**: Tüm sayfa geçişlerini yöneten navigasyon yapılandırması

## 🔄 Veri Akışı

```
1. LoginPage → Authentication
2. HomePage → Firestore'dan mektupları çek
3. CreatorPage → Mektup türü seç
4. CreateQuestionStage/CreateMysteryStage → Aşamaları oluştur
5. FinalLetterEditor → Mektubu Firestore'a kaydet
6. LetterPlayPage → Mektup çözme süreci
7. StageManager → Aşama yönetimi
8. QuestionStage/MysteryStage → Kullanıcı etkileşimi
9. FinalLetter → Başarı gösterimi
```

## 🎨 Stil Yapısı

Her bileşen kendi StyleSheet'ini içerir:
- Pastel renk paleti (#f0f4f8, #4299e1, #48bb78)
- Modern border-radius değerleri (12-20px)
- Shadow/elevation efektleri
- Responsive tasarım

## 📊 Veri Modelleri

### Letter Document
```javascript
{
  userId: string,
  title: string,
  type: "question" | "mystery",
  stages: Array<Stage>,
  finalLetter: { title: string, content: string },
  currentStage: number,
  isCompleted: boolean,
  createdAt: timestamp,
  updatedAt: timestamp
}
```

### LetterAttempt Document
```javascript
{
  userId: string,
  letterId: string,
  lockedUntil: timestamp | null,
  wrongAnswers: number,
  createdAt: timestamp,
  updatedAt: timestamp
}
```

## 🔧 Geliştirme Komutları

```bash
npm start          # Expo development server başlat
npm run android    # Android emulator'da çalıştır
npm run ios        # iOS simulator'da çalıştır
npm run web        # Web tarayıcısında çalıştır
```

## 📦 Ana Bağımlılıklar

- **React Native**: Mobil uygulama framework'ü
- **Expo**: Geliştirme ve dağıtım platformu
- **Firebase**: Backend servisleri
- **React Navigation**: Sayfa yönlendirme
- **React Native Gesture Handler**: Dokunma etkileşimleri
- **React Native Safe Area Context**: Güvenli alan yönetimi

## 🚀 Deployment

Uygulama Expo platformu üzerinden deploy edilebilir:
- **Expo Go**: Geliştirme ve test için
- **Expo Build**: Production build'ler için
- **App Store/Play Store**: Mağaza dağıtımı için

---

Bu yapı, ölçeklenebilir ve sürdürülebilir bir React Native uygulaması için optimize edilmiştir.
