# 💌 Mystery Letter - G<PERSON>m Mektubu Uygulaması

Modern React Native ile geliştirilmiş, sevdikle<PERSON>z için özel mektuplar oluşturabileceğiniz bir uygulama.

## 🚀 Özellikler

### 🔐 İki Farklı Mektup Türü
- **Şifreli Sorular**: Soru-cevap tabanlı aşamalar
- **Dedektiflik Gizemi**: Hikaye tabanlı bulmacalar

### 📱 Modern Kullanıcı Arayüzü
- Pastel renkler ve modern tasarım
- Animasyonlu geçişler
- Responsive tasarım

### 🔒 Güvenlik Sistemi
- Yanlış cevap durumunda 24 saat kilitleme
- Firestore ile veri saklama

### 📊 Aşama Yönetimi
- Çoklu aşama desteği
- İlerleme takibi
- Özel son mektup

## 🛠️ Teknolojiler

- **React Native** - Mobil uygulama geliştirme
- **Expo** - Geliştirme ve dağıtım platformu
- **Firebase** - Backend servisleri
  - Firestore (Veritabanı)
- **React Navigation** - Sayfa yönlendirme

## 📦 Kurulum

### Gereksinimler
- Node.js (v14 veya üzeri)
- npm veya yarn
- Expo CLI
- Firebase projesi

### Adımlar

1. **Projeyi klonlayın**
```bash
git clone <repository-url>
cd MysteryLetter
```

2. **Bağımlılıkları yükleyin**
```bash
npm install
```

3. **Firebase yapılandırması**
- Firebase Console'da yeni bir proje oluşturun
- Firestore'u etkinleştirin
- `src/config/firebase.js` dosyasındaki yapılandırmayı güncelleyin

4. **Uygulamayı başlatın**
```bash
npm start
```

## 🔧 Firebase Yapılandırması

### Firestore Kuralları
```javascript
rules_version = '2';
service cloud.firestore {
  match /databases/{database}/documents {
    // Letters collection - herkes okuyabilir ve yazabilir
    match /letters/{document} {
      allow read, write: if true;
    }

    // Letter attempts collection - herkes okuyabilir ve yazabilir
    match /letterAttempts/{document} {
      allow read, write: if true;
    }
  }
}
```

## 📱 Uygulama Yapısı

```
src/
├── components/          # Yeniden kullanılabilir bileşenler
│   ├── LetterCard.jsx   # Mektup kartı
│   ├── StageManager.jsx # Aşama yöneticisi
│   ├── QuestionStage.jsx # Soru aşaması
│   └── MysteryStage.jsx # Gizem aşaması
├── config/
│   └── firebase.js      # Firebase yapılandırması
├── navigation/
│   └── AppNavigator.js  # Ana navigasyon
└── screens/             # Uygulama sayfaları
    ├── HomePage.jsx     # Ana sayfa
    ├── CreatorPage.jsx  # Mektup türü seçimi
    ├── CreateQuestionStage.jsx # Soru aşaması oluşturma
    ├── CreateMysteryStage.jsx  # Gizem aşaması oluşturma
    ├── FinalLetterEditor.jsx   # Son mektup editörü
    ├── LetterPlayPage.jsx      # Mektup çözme
    └── FinalLetter.jsx         # Başarı sayfası
```

## 🎮 Kullanım

### 1. Mektup Oluşturma
- "+" butonuna tıklayın
- Mektup türünü seçin (Şifreli Sorular veya Dedektiflik Gizemi)
- Aşamaları ekleyin
- Son mektubu yazın

### 2. Mektup Çözme
- Ana sayfadan bir mektuba tıklayın
- Aşamaları sırayla çözün
- Yanlış cevap verirseniz 24 saat bekleyin
- Tüm aşamaları çözerek özel mektuba ulaşın

## 🎨 Tasarım Özellikleri

- **Renk Paleti**: Pastel tonlar (#f0f4f8, #4299e1, #48bb78)
- **Tipografi**: Modern ve okunabilir fontlar
- **Animasyonlar**: Yumuşak geçişler ve etkileşimler
- **İkonlar**: Emoji tabanlı görsel dil

## 🔐 Güvenlik

- Veri şifreleme
- Yanlış cevap koruması
- Firebase Firestore güvenlik kuralları

## 📱 Platform Desteği

- ✅ iOS
- ✅ Android  
- ✅ Web (Expo Web)

## 🤝 Katkıda Bulunma

1. Fork edin
2. Feature branch oluşturun (`git checkout -b feature/amazing-feature`)
3. Commit edin (`git commit -m 'Add amazing feature'`)
4. Push edin (`git push origin feature/amazing-feature`)
5. Pull Request oluşturun

## 📄 Lisans

Bu proje MIT lisansı altında lisanslanmıştır.

## 🆘 Destek

Herhangi bir sorun yaşarsanız:
1. GitHub Issues'da sorun bildirin
2. Dokümantasyonu kontrol edin
3. Firebase Console'da logları inceleyin

---

💌 **Mystery Letter** ile sevdiklerinize özel anılar yaratın!
