import React from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  StyleSheet,
} from 'react-native';

const LetterCard = ({ letter, onPress }) => {
  const getStatusIcon = () => {
    if (letter.isCompleted) {
      return '✅';
    } else if (letter.lockedUntil && new Date(letter.lockedUntil.toDate()) > new Date()) {
      return '🔒';
    } else {
      return '⏳';
    }
  };

  const getStatusText = () => {
    if (letter.isCompleted) {
      return 'Çözüldü';
    } else if (letter.lockedUntil && new Date(letter.lockedUntil.toDate()) > new Date()) {
      const lockDate = new Date(letter.lockedUntil.toDate());
      const now = new Date();
      const diffHours = Math.ceil((lockDate - now) / (1000 * 60 * 60));
      return `${diffHours} saat kilitli`;
    } else {
      return `${letter.currentStage}/${letter.stages.length} aşama`;
    }
  };

  const getStatusColor = () => {
    if (letter.isCompleted) {
      return '#48bb78';
    } else if (letter.lockedUntil && new Date(letter.lockedUntil.toDate()) > new Date()) {
      return '#a0aec0';
    } else {
      return '#4299e1';
    }
  };

  const formatDate = (timestamp) => {
    if (!timestamp) return '';
    const date = timestamp.toDate ? timestamp.toDate() : new Date(timestamp);
    return date.toLocaleDateString('tr-TR', {
      day: 'numeric',
      month: 'long',
      year: 'numeric',
    });
  };

  return (
    <TouchableOpacity style={styles.card} onPress={onPress}>
      <View style={styles.cardHeader}>
        <View style={styles.titleContainer}>
          <Text style={styles.title} numberOfLines={2}>
            {letter.title || 'Başlıksız Mektup'}
          </Text>
          <Text style={styles.date}>
            {formatDate(letter.createdAt)}
          </Text>
        </View>
        <View style={[styles.statusContainer, { backgroundColor: getStatusColor() + '20' }]}>
          <Text style={styles.statusIcon}>{getStatusIcon()}</Text>
        </View>
      </View>

      <View style={styles.cardBody}>
        <Text style={[styles.statusText, { color: getStatusColor() }]}>
          {getStatusText()}
        </Text>
        <Text style={styles.typeText}>
          {letter.type === 'question' ? '🔐 Şifreli Sorular' : '🔍 Dedektiflik Gizemi'}
        </Text>
      </View>

      <View style={styles.progressContainer}>
        <View style={styles.progressBar}>
          <View
            style={[
              styles.progressFill,
              {
                width: `${(letter.currentStage / letter.stages.length) * 100}%`,
                backgroundColor: getStatusColor(),
              },
            ]}
          />
        </View>
        <Text style={styles.progressText}>
          {letter.currentStage}/{letter.stages.length}
        </Text>
      </View>
    </TouchableOpacity>
  );
};

const styles = StyleSheet.create({
  card: {
    backgroundColor: 'white',
    borderRadius: 16,
    padding: 20,
    marginBottom: 16,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 4,
  },
  cardHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    marginBottom: 12,
  },
  titleContainer: {
    flex: 1,
    marginRight: 12,
  },
  title: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#2d3748',
    marginBottom: 4,
  },
  date: {
    fontSize: 14,
    color: '#718096',
  },
  statusContainer: {
    width: 40,
    height: 40,
    borderRadius: 20,
    justifyContent: 'center',
    alignItems: 'center',
  },
  statusIcon: {
    fontSize: 20,
  },
  cardBody: {
    marginBottom: 16,
  },
  statusText: {
    fontSize: 16,
    fontWeight: '600',
    marginBottom: 4,
  },
  typeText: {
    fontSize: 14,
    color: '#718096',
  },
  progressContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  progressBar: {
    flex: 1,
    height: 6,
    backgroundColor: '#e2e8f0',
    borderRadius: 3,
    marginRight: 12,
  },
  progressFill: {
    height: '100%',
    borderRadius: 3,
  },
  progressText: {
    fontSize: 12,
    color: '#718096',
    fontWeight: '600',
  },
});

export default LetterCard;
